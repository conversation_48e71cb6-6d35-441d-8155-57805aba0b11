.traceBlock__container {
    margin-top: 12px;
    border-radius: 8px;
    background: var(--home-bg);
}
.traceBlock__itemContainer {
    &--more {
        padding-left: 23px;
    }

    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 18px;

    .traceBlock__groupTitle {
        // margin-left: -8px;
        color: #333333;
        font-weight: 500;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 6px;
    }
    .ignore_traceBlock__item {
        display: flex;
        justify-content: space-between;
        gap: 10px;
        font-size: 14px;
        line-height: 17px;
        .traceBlock__label {
            display: flex;
            width: 46%;
            color: #666666;
        }
        .traceBlock__value {
            flex: 1;
            display: flex;
            justify-content: flex-end;
            word-break: break-all;
            color: #222222;
            // text-align: right;
            text-align: justify;
        }
    }
}

.showMoreBtn {
    --border-width: 0;
    --text-color: var(--primary-color);
    --background-color: #dee5ff;
    padding: 9px 12px;
    font-size: 12px;
    margin-top: 20px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
}
// 种植过程

.zhong {
    width: 18px;
    height: 14px;
    display: inline-block;
    background: var(--title-child-bg-img2);
    background-size: contain;
}
.sheng {
    width: 14px;
    height: 14px;
    display: inline-block;
    background: var(--title-child-bg-img1);
    background-size: contain;
}
.shengDetail {
    width: 14px;
    height: 14px;
    display: inline-block;
    background: var(--title-child-bg-img3);
    background-size: contain;
}
