import { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import {
    Button,
    Input,
    TextArea,
    Picker,
    Space,
    Image,
    DotLoading,
    Toast,
    Ellipsis
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    UpOutline,
    DownOutline,
    LeftOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import { useRequest, useSetState } from "ahooks";
import { useImmer } from "use-immer";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import CollapseTable from "@/components/collapse-table";
import QRCodeScan from "@/components/qrcode-scan";
import EllipsisTraceCode from "@/components/ellipsis-trace-code";

import { showConfirmModal } from "@/utils";
import { useScanQRCode } from "@/hooks";
import { bSideRequests } from "@/services";

import iconScanQrCode from "@/assets/imgs/scan-qrcode.png";
import { ReactComponent as SvgScanQrCode } from "@/assets/imgs/scan-qrcode.svg";

import styles from "./index.module.less";

interface IScanPackState {
    boxCode: string | null;
    traceCode: {
        id: number;
        code: string;
        name: string;
    }[];
}

export default () => {
    const navigate = useNavigate();
    const { id } = useParams();

    const getPackCodeDetailRequest = useRequest(
        () => {
            if (!id) {
                throw new Error("缺少id");
            }
            return bSideRequests.getPackCodeDetail(id);
        },
        {
            refreshDeps: [id],
        },
    );
    const packCodeDetail = getPackCodeDetailRequest.data?.data?.data || {};

    const deprecatePackRequest = useRequest(
        () => {
            if (!id) {
                throw new Error("缺少id");
            }
            return bSideRequests.deprecatePack(id);
        },
        {
            manual: true,
            onSuccess() {
                Toast.show({
                    icon: "success",
                    content: "作废成功",
                });
                getPackCodeDetailRequest.refresh();
            },
        },
    );

    if (getPackCodeDetailRequest.loading) {
        return (
            <div
                style={{
                    paddingTop: "50px",
                    textAlign: "center",
                    color: "var(--adm-color-weak)",
                }}
            >
                <div style={{ fontSize: 24, marginBottom: 24 }}>
                    <DotLoading />
                </div>
                正在加载数据
            </div>
        );
    }

    return (
        <div className={`${styles.ScanPack}`}>
            <div className="scanBoxRetContainer outTable-extraPadding ">
                <div className="scanRetHeader">箱码</div>
                <div className="scanRet__code">
                    {packCodeDetail?.boxCode || "-"}
                </div>
            </div>
            <div className="scanBoxRetContainer" style={{ marginTop: 22 }}>
                <div className="scanRetHeader outTable-extraPadding">
                    溯源码
                    {packCodeDetail?.traceCodeList?.length &&
                        packCodeDetail.traceCodeList.length > 0 &&
                        `（共${packCodeDetail.traceCodeList.length}个）`}
                </div>
                <CollapseTable
                    columns={[
                        {
                            title: "产品名",
                            dataIndex: "productName",
                            width: "40%",
                        },
                        {
                            title: "溯源码",
                            dataIndex: "code",
                            width: "50%",
                            render(code: string) {
                                return (
                                    <EllipsisTraceCode codeText={code}/>
                                );
                            },
                        },
                        {
                            width: "10%",
                            style: {
                                display: "flex",
                                justifyContent: "flex-end",
                            },
                        },
                    ]}
                    dataSource={packCodeDetail?.traceCodeList || []}
                ></CollapseTable>
            </div>
            {packCodeDetail?.state === 1 && (
                <Button
                    loading={deprecatePackRequest.loading}
                    style={{ marginTop: 50 }}
                    shape="rounded"
                    block
                    color="primary"
                    type="submit"
                    onClick={() => {
                        showConfirmModal({
                            title: "确认作废",
                            content: "确认作废吗",
                            confirmBtnProps: {
                                color: "danger",
                            },
                            onConfirm() {
                                deprecatePackRequest.run();
                            },
                        });
                    }}
                >
                    作废
                </Button>
            )}
        </div>
    );
};
