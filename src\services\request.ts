import axios, { AxiosRequestConfig, AxiosResponse } from "axios";
import { Toast } from "antd-mobile";

import { apiBaseUrl } from "@/config";
import useStore from "@/store";

export const instance = axios.create({
    baseURL: `${apiBaseUrl}`,
    timeout: 30000,
});

const requestErrorHandler = (err: any) => {
    console.log(err, "requestErrorHandler");
    const timeout = err?.config?.timeout;
    const errMsg = err?.response?.data?.message;
    if (err?.message === `timeout of ${timeout}ms exceeded`) {
        Toast.show({
            content: "请求超时",
        });
    } else if (!err.response.config.__disableGlobalToast) {
        Toast.show({
            content: errMsg || "请求失败",
        });
    }
};

instance.interceptors.request.use(
    (config) => {
        const jwt = sessionStorage.getItem("jwt");
        if (jwt) {
            config.headers.token = jwt;
        }
        return config;
    },
    (err) => {
        return Promise.reject(err);
    },
);

instance.interceptors.response.use(
    (response: AxiosResponse<any, any>) => {
        return new Promise((resolve, reject) => {
          console.log(response,'333333333333')
            if (response.headers["content-type"] === "application/json") {
                if (response?.data?.code === 200) {
                    resolve(response);
                } else if(response?.config?.url === '/user/sendCode'||response?.config?.url === '/login'||response?.config?.url ==='/process/add'|| response?.config?.url?.includes('/chain/replay')) {
                    // requestErrorHandler({ response });
                    reject({ response });
                }
                 else if (response?.data?.code === 5001010017|| response?.data?.code ===5001020010) {
                    useStore.setState({
                        isLogin: false,
                        userInfo: null,
                    });
                    sessionStorage.clear();
                    Toast.show({
                        content: response?.data?.message,
                    });
                    return reject(response);
                }else {
                  requestErrorHandler({ response });
                  reject({ response });
              }

            } else {
                if (response?.data?.code === 5001010017 || response?.data?.code ===5001020010) {
                    useStore.setState({
                        isLogin: false,
                        userInfo: null,
                    });
                    sessionStorage.clear();
                    Toast.show({
                        content: response?.data?.message,
                    });
                    return reject(response);
                }else{
                    resolve(response);
                }
            }
        });
    },
    (err) => {
        requestErrorHandler(err);
        if (err.response.status === 401) {
            useStore.setState({
                isLogin: false,
                userInfo: null,
            });
            sessionStorage.clear();
        }
        return Promise.reject(err);
    },
);

export default instance.request;
