.imageUploader {
    :global {
        .adm-image-uploader-cell {
            border-radius: var(--border-radius);
        }
    }
}

.uploadBtn {
    width: var(--cell-size);
    height: var(--cell-size);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    // border: 1px dashed rgba(0, 0, 0, 0.15);
    border: 1px solid var(--tab-color);
    border-radius: var(--border-radius);
    font-size: var(--text-md);
    background: var(--tab-color-Bg);
    color: var(--tab-color);
    .uploadBtn__addIcon {
        font-size: 24px;
        margin-bottom: 10px;
        color: var(--tab-color);
    }
}

.uploadHelp {
    margin-top: 9px;
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.videoPreviewModal {
    --adm-center-popup-max-width: 100vw;
    :global {
        .adm-center-popup-wrap {
            width: 100%;
        }
        .adm-modal-body {
            padding-top: 0;
            background: none;
            border-radius: 0px;
        }
        .adm-modal-content {
            padding: 0;
            .videoContainer {
                width: 100%;
                display: flex;
                justify-content: center;
            }
            video {
                max-width: 100%;
                max-height: 70vh;
            }
        }
        .adm-modal-footer-empty {
            height: 0;
        }
    }
}
