import { useState, useCallback, useRef, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
    Button,
    Input,
    TextA<PERSON>,
    Picker,
    Space,
    Popup,
    CheckList,
    Toast,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    UpOutline,
    DownOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import useUrlState from "@ahooksjs/use-url-state";
import { useImmer } from "use-immer";
import { useRequest, useSetState } from "ahooks";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import CollapseTable from "@/components/collapse-table";
import CheckListPopup from "@/components/check-list-popup";
import BoxCodeScanner from "@/components/box-code-scanner";

import iconScanQrCode from "@/assets/imgs/scan-qrcode.png";
import { ReactComponent as SvgScanQrCode } from "@/assets/imgs/scan-qrcode.svg";

import { TRANSPORTATION_TYPE_CONSTANTS } from "@/config";
import { useConfirmBlock, useScanQRCode } from "@/hooks";
import {
    bSideRequests,
    getPackCodeInfoByPackCode,
    BoxCodeVerifyModule,
} from "@/services";
import {
    showToastLoading,
    signatureByEncryptedPrivateKey,
    showConfirmModal,
    requestDataSign,
} from "@/utils";

import styles from "./index.module.less";

import type {
    unstable_Blocker as Blocker,
    unstable_BlockerFunction as BlockerFunction,
} from "react-router-dom";
import type { ToastLoadingHandler } from "@/utils";
import type { BoxCodeData } from "@/components/box-code-scanner";

const SelfTransportationEntry = () => {
    const navigate = useNavigate();
    const location = useLocation();

    const [urlState, setUrlState] = useUrlState<{
        step: "form" | "scan";
    }>({
        step: "form",
    });

    useEffect(() => {
        navigate(location.pathname + "?step=form", {
            replace: true,
        });
    }, []);

    const [transportationInfoForm] = Form.useForm();

    const [scannedBoxCodeInfo, setScannedBoxCodeInfo] = useState<BoxCodeData>(
        [],
    );

    const toastLoadingHandlerRef = useRef<ToastLoadingHandler | null>(null);
    const transportationScanEntryRequest = useRequest(
        bSideRequests.transportationScanEntry,
        {
            manual: true,
            onSuccess() {
                toastLoadingHandlerRef?.current?.close?.();
                transportationInfoForm.resetFields();
                setScannedBoxCodeInfo([]);
                Toast.show({
                    icon: "success",
                    content: "上报成功",
                });
                isFormFinished.current = true;
                navigate(-1);
            },
            onError(ret: any) {
                toastLoadingHandlerRef?.current?.close?.();
                const errMsg = ret?.response?.data?.message || "上报失败";
                isFormFinished.current = true;
                navigate(`result?msg=${errMsg}`);
            },
        },
    );

    const isFormFinished = useRef(true);
    const shouldBlock = useCallback<BlockerFunction>(
        ({ currentLocation, nextLocation }) => {
            return (
                !isFormFinished.current &&
                currentLocation.pathname !== nextLocation.pathname
            );
        },
        [],
    );
    useConfirmBlock({
        content: "离开后编辑内容不保存，确认离开吗？",
        shouldBlock: shouldBlock,
    });

    const renderOutboundForm = (isShow: boolean) => {
        return (
            <div
                style={{
                    display: isShow ? "block" : "none",
                }}
            >
                <Form
                    form={transportationInfoForm}
                    layout="horizontal"
                    onFinish={(values) => {
                        setUrlState({
                            step: "scan",
                        });
                    }}
                    onFinishFailed={(failInfo) => {
                        const errorMsg =
                            failInfo?.errorFields?.[0]?.errors?.[0];
                        Toast.show(errorMsg);
                    }}
                    onValuesChange={() => {
                        isFormFinished.current = false;
                    }}
                    footer={
                        <Button
                            type="submit"
                            style={{ marginTop: 100 }}
                            color="primary"
                            shape="rounded"
                            block
                        >
                            开始扫描
                        </Button>
                    }
                >
                    <Form.Item
                        label="物流企业"
                        name="loEnterprises"
                        rules={[
                            { required: true, message: "物流企业不能为空" },
                            {
                                max: 50,
                                message: "不能超过50位",
                            },
                        ]}
                    >
                        <Input placeholder="点击输入物流企业名称" />
                    </Form.Item>
                    <Form.Item
                        label="物流单号"
                        name="loNumber"
                        rules={[
                            { required: true, message: "物流单号不能为空" },
                            {
                                max: 50,
                                message: "不能超过50位",
                            },
                            {
                                pattern: /^[a-zA-Z0-9]*$/,
                                message: "只能输入数字+大小写字母",
                            },
                        ]}
                    >
                        <Input placeholder="点击输入物流单号" />
                    </Form.Item>
                </Form>
            </div>
        );
    };

    const handleEntryRequest = async () => {
        const transportationInfo = transportationInfoForm.getFieldsValue();
        console.log(
            transportationInfo,
            scannedBoxCodeInfo,
            "handleEntryRequest",
        );
        try {
            const requestData = {
                type: 2,
                loEnterprises: transportationInfo?.loEnterprises,
                loNumber: transportationInfo?.loNumber,
                boxList: scannedBoxCodeInfo.map((item) => item.code),
            } as const;
            const signedData = await requestDataSign(
                requestData,
                "addLogisticsVo",
            );
            toastLoadingHandlerRef.current = showToastLoading({
                duration: 0,
                content: "正在上传",
            });
            transportationScanEntryRequest.run(signedData);
        } catch (err: any) {
            console.log(err, "err");
            Toast.show({
                icon: "fail",
                content: err?.toString?.() || "加密失败",
            });
        }
    };

    const renderScanView = () => {
        return (
            <div>
                <BoxCodeScanner
                    module={BoxCodeVerifyModule.LOGISTICS}
                    value={scannedBoxCodeInfo}
                    onChange={(data) => {
                        setScannedBoxCodeInfo(data);
                    }}
                ></BoxCodeScanner>
                <Button
                    loading={transportationScanEntryRequest.loading}
                    onClick={() => {
                        if (scannedBoxCodeInfo.length === 0) {
                            Toast.show("请扫描箱码");
                            return;
                        }
                        showConfirmModal({
                            title: "确认上传",
                            onConfirm() {
                                handleEntryRequest();
                            },
                        });
                    }}
                    style={{ marginTop: 100 }}
                    color="primary"
                    shape="rounded"
                    block
                >
                    上报
                </Button>
            </div>
        );
    };

    return (
        <div className={`${styles.SelfTransportationEntry}`}>
            {renderOutboundForm(urlState.step === "form")}
            {urlState.step === "scan" && renderScanView()}
        </div>
    );
};

export default SelfTransportationEntry;
