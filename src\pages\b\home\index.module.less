//out:false
.home {
    background:
        var(--page-bg-img1) no-repeat center top/100%,
        var(--page-bg-color);

    :global {
        .main {
            // background:
            // url("@/assets/imgs/bimgs/info-background.png") no-repeat center ;
            flex: 1;
            padding: 150px 6px 10px 6px;

            .home__welcome {
                padding: 14px 15px;
                background: var(--page-block-color);
                border-radius: var(--border-radius);
                font-size: var(--text-md);
                display: flex;
                flex-direction: column;
                gap: 4px;
                .text {
                    color: var(--tab-color);
                    display: flex;
                    justify-content: space-between;

                    div:nth-child(2) {
                        cursor: pointer;
                    }
                }
            }

            .home__menu {
                margin-top: 12px;
                background: var(--page-block-color);
                border-radius: var(--border-radius);

                &--item {
                    padding-top: 100%;
                    position: relative;

                    button {
                        position: absolute;
                        top: 0;
                        width: 100%;
                        height: 100%;
                        font-size: calc(var(--text-md) + 1px);

                        .menu__item--icon {
                            margin-bottom: 19px;
                        }
                    }
                }
            }
        }
    }
}
