//out:false
.scanInbound {
    margin: 10px;
    padding: 20px 16px;
    padding-bottom: 30px;
    background: var(--page-block-color);
    border-radius: var(--border-radius);

    :global {
        // .adm-image-uploader-upload-button-wrap{
        //   background: #FDEEC9;
        //   color: #FFB200;
        // }
        .adm-text-area {
            background: var(--tab-color-Bg);
        }
    }

    ._uploadBtn_g8r4v_4 {
        border: 1px solid var(--tab-color) !important;
        background: var(--tab-color-Bg) !important;
        color: var(--tab-color) !important;
    }
    .Btn {
        height: 60px;
        margin-top: 25px;
        // border-radius: 10px;
        // padding: 13px 12px 15px 12px;
        font-size: 16px;
        line-height: 19px;
        letter-spacing: 0px;
        background-image: url("@/assets/imgs/bimgs/btn1.png");
        background-size: 104% 106%;
        background-position: center 3px;
        background-repeat: no-repeat;
        // background-color: transparent;
        // background: red;
        border: 0;

        span {
            color: white;
        }
    }
}

/* HTML: <div class="loader"></div> */
.loader {
    margin: 25px auto;
    width: 85px;
    height: 23px;
    border: none;
    --g1: conic-gradient(from 90deg at left 0px top 0px, #76ae55 90deg, #fff 0);
    --g2: conic-gradient(
        from -90deg at bottom 3px right 3px,
        #76ae55 90deg,
        #fff 0
    );
    background:
        var(--g1) left no-repeat,
        var(--g1) center no-repeat,
        var(--g1) right no-repeat,
        var(--g2) left no-repeat,
        var(--g2) center no-repeat,
        var(--g2) right no-repeat;
    background-position: left, center, right;
    background-repeat: no-repeat;
    animation: l8 1s infinite;
}
@keyframes l8 {
    0% {
        background-size:
            25px 100%,
            25px 100%,
            25px 100%;
    }
    20% {
        background-size:
            25px 50%,
            25px 100%,
            25px 100%;
    }
    40% {
        background-size:
            25px 50%,
            25px 50%,
            25px 100%;
    }
    60% {
        background-size:
            25px 100%,
            25px 50%,
            25px 50%;
    }
    80% {
        background-size:
            25px 100%,
            25px 100%,
            25px 50%;
    }
    100% {
        background-size:
            25px 100%,
            25px 100%,
            25px 100%;
    }
}
