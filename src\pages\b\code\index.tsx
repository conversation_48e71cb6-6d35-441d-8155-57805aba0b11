// import React from 'react'
import "./index.less";
import { useNavigate } from "react-router-dom";
import { Image } from "antd-mobile";
import Tx from "@/assets/imgs/bimgs/tx.png";
import useStore from "@/store";

import dayjs from "dayjs";
import { NavBar, Toast } from "antd-mobile";
import { useEffect, useState } from "react";
import { bSideRequests } from "@/services";

export default () => {
    // const userInfo = useStore<any>((state) => state.userInfo);
    const userInfo = JSON.parse(sessionStorage.userInfo);
    const [ImageUrl, setDownloadedImageUrl] = useState<any>();

    const navigate = useNavigate();
    const back = () => {
        navigate("/b/home");
    };
    const handledetail = () => {
        navigate("/b/personalinfo"); //
    };
    useEffect(() => {
        bSideRequests.usercode().then((res) => {
            console.log(res);
            const blob = new Blob([res.data], { type: "image/jpeg" });
            const imageUrl = URL.createObjectURL(blob);
            setDownloadedImageUrl(imageUrl);
        });
    }, []);
    return (
        <div className="code_info_box">
            <div className="back_img">
                <NavBar className="nav" onBack={back}>
                    个人信息
                </NavBar>
            </div>
            <div className="info_div">
                <div className="avatar_div">
                    <div className="avatar">
                        <Image src={Tx}></Image>
                    </div>
                    <p className="user_name">{userInfo && userInfo.userName}</p>
                </div>

                <div className="info_text">
                    <div className="code_style">
                        <Image src={ImageUrl}></Image>
                    </div>
                    <p className="look_style" onClick={handledetail}>
                        <a>查看个人信息&gt; &gt; </a>
                    </p>
                </div>
            </div>
        </div>
    );
};
