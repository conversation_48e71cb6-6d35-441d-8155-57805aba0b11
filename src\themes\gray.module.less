//out:false
.themeVariables {
    /* custom */
    --primary-color: #765c30;
    --secondary-color: #94b1f3;
    --dangerous-color: #ff6161;
    --support-color: #d8d8d8;
    --text-main-title: #fff;
    --page-bg-color: #fafafa;
    --page-block-color: #fff;
    --text-main: #fff;
    --text-secondary: #909090;
    --home-bg-bottom: #f2f0ed;
    --page-block-padding: 10px;
    --border-radius: 8px;
    --text-sm: 12px;
    --text-md: 14px;
    --text-lg: calc(var(--text-md) + 4px);
    --home-bg: #f3f1ee;
    --tab-color: #765c30;
    --text-color: #4d4d4d;
    --tabs-primary-color: #765c30;
    --tab-color-Bg: #fff6e7;
    --su-code: #fff6e7;
    --btn-color-Bg: #bba785;
    --btn-bg-img-nui: url("@/assets/qin/btn.png"); // 点击溯源按钮背景
    --home-bg-logo: url("@/assets/qin/logo.png") no-repeat; //按钮下的logo
    --bg-color: #2390668a;
    --btn-bg-suss: rgba(128, 169, 50, 0.4);
    --btn-bg-info: rgba(255, 170, 0, 0.4);
    --page-bg-img: url("@/assets/imgs/bimgs/backgroung.png"); //
    // --page-bg-img: url("@/assets/imgs/page-bg-blue.png");
    --title-bg-1: url("@/assets/qin/yuan4.png") no-repeat; //标题后面的原点

    --title-bg-img-1: url("@/assets/qin/title/chan.png") no-repeat; //产品信息
    --title-bg-img-2: url("@/assets/qin/title/zhong.png") no-repeat; // 种植溯源
    --title-bg-img-3: url("@/assets/qin/title/sheng11.png") no-repeat; // 生产溯源
    --title-bg-img-4: url("@/assets/qin/title/chan1.png") no-repeat; // 产品说明
    --title-bg-img-5: url("@/assets/qin/title/chan1.png") no-repeat; // 产地详情
    --title-bg-img-6: url("@/assets/qin/title/zhi.png") no-repeat; // 质检信息
    --title-bg-img-7: url("@/assets/qin/title/qi.png") no-repeat; // 企业基础信息
    --title-bg-img-8: url("@/assets/qin/title/sheng.png") no-repeat; // 生产商

    --info-bg-img1: url("@/assets/qin/backgroung.png");
    // --info-bg-img2: url("@/assets/imgs/bimgs/info-background.png");
    --info-bg-img2: url("@/assets/qin/backgroung2.png");
    // 详情子标题左侧图标
    --title-child-bg-img1: url("@/assets/qin/title/zhong1.png") no-repeat;
    --title-child-bg-img2: url("@/assets/qin/title/sheng1.png") no-repeat;
    --title-child-bg-img3: url("@/assets/qin/title/sheng2.png") no-repeat;

    // 风险提示
    --sess-bg-img: url("@/assets/imgs/c/sess.png") no-repeat;
    --info-bg-img: url("@/assets/imgs/c/info.png") no-repeat;
    --border-line: #88a644;
    /* antd-mobile */
    --adm-button-border-radius: var(--border-radius);
    --adm-color-primary: var(--tab-color);
    --placeholder-color: var(--text-secondary);
    :global {
        .adm-input {
            --placeholder-color: var(--text-secondary);
        }
    }
}
