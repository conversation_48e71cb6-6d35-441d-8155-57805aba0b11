import React from "react";

import styles from "./index.module.less";

interface ITraceBlockTitleProps {
    children: React.ReactNode;
}
function TraceBlockTitle(props: ITraceBlockTitleProps) {
    const { children } = props;
    const titleBackgroundMap = {
        产品信息: "--title-bg-img-1",
        种植溯源: "--title-bg-img-2",
        生产溯源: "--title-bg-img-3",
        产品说明: "--title-bg-img-4",
        产地说明: "--title-bg-img-5",
        产地详情: "--title-bg-img-5",
        质检信息: "--title-bg-img-6",
        企业基础信息: "--title-bg-img-7",
        生产商: "--title-bg-img-8",
        // 产品说明: "--title-bg-img-2",
        // Add more mappings as needed
    };
    // 将 children 转换为字符串，并去除前后空白
    const childString = String(children).trim();

    // 获取对应的 CSS 变量名，默认为 undefined 或者你可以设置一个默认值
    const bgVar = titleBackgroundMap[childString];

    return (
        <div
            className={styles.TraceBlockTitle}
            style={{
                background: bgVar ? `var(${bgVar})` : "none",
                backgroundSize:
                    bgVar == "--title-bg-img-7" ? "100% 50%" : "90% 50%",
                backgroundPosition: "center 12px",
            }}
        >
            {/* <div className={styles.diamond}></div> */}
            <div className={styles.title_text}>{children}</div>
            {/* <div className={styles.diamond}></div> */}
        </div>
    );
}

export default TraceBlockTitle;
