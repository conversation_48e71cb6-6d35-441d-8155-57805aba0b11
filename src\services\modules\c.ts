import request from "@/services";

export const getTraceDataByTraceCodeId = (id: string) => {
    return request({
        url: "/trace-code/trace/data",
        method: "get",
        params: {
            id,
        },
        // @ts-ignore
        __disableGlobalToast: true,
    });
};

export const getProductTraceDataByTraceCodeId = (id: string) => {
    return request({
        url: "/trace-code/trace/product",
        method: "get",
        params: {
            id,
        },
    });
};

export const getInspectionTraceDataByTraceCodeId = (id: string) => {
    return request({
        url: "/trace-code/trace/inspection",
        method: "get",
        params: {
            id,
        },
    });
};

export const getOrgTraceDataByTraceCodeId = (id: string) => {
    return request({
        url: "/trace-code/trace/org",
        method: "get",
        params: {
            id,
        },
    });
};

export interface IGetTraceQueryRecordProps {
    codeId: number | string;
    pageIndex: number;
    pageSize: number;
}
export const getTraceQueryRecord = (props: IGetTraceQueryRecordProps) => {
    return request({
        url: "/trace-code/queryRecord",
        method: "post",
        data: props,
    });
};

export const getTransactionChainInfo = (transactionId: string) => {
    return request({
        url: "/trace-code/getChainHis",
        method: "get",
        params: {
            transactionId,
        },
    });
};
