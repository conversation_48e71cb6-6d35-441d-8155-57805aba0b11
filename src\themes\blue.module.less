//out:false
.themeVariables {
    /* custom */
    --primary-color: #88a644;
    --secondary-color: #94b1f3;
    --dangerous-color: #ff6161;
    --support-color: #d8d8d8;
    --home-bg: #ffffff;
    --page-bg-color: #fafafa;
    --page-block-color: #fff;
    --text-main: #333;
    --text-secondary: #909090;
    --text-main-title: #333;
    --home-bg-bottom: #f2f8f2;
    --page-block-padding: 10px;
    --border-radius: 8px;
    --text-sm: 12px;
    --text-md: 14px;
    --text-lg: calc(var(--text-md) + 4px);
    --page-bg-img: url("@/assets/imgs/bimgs/backgroung.png"); //
    --page-bg-img1: url("@/assets/imgs/bimgs/bhome.png"); // 首页背景
    --tab-color: #ffaa00;
    --text-color: #4d4d4d;
    --tabs-primary-color: #88a644;
    --tab-color-Bg: #fdeec9;
    --btn-color-Bg: #fdeec9;
    --su-code: #e5edd5;
    --btn-bg-img-nui: url("@/assets/imgs/bimgs/btn3.png"); // 点击溯源按钮背景
    --home-bg-logo: url("@/assets/qin/logo1.png") no-repeat; //按钮下的logo
    --bg-color: #2390668a;
    --btn-bg-suss: rgba(128, 169, 50, 0.8);
    --btn-bg-info: rgba(255, 170, 0, 0.8);
    --page-bg-img: url("@/assets/imgs/bimgs/backgroung.png"); //
    // --page-bg-img: url("@/assets/imgs/page-bg-blue.png");
    --title-bg-1: url("@/assets/imgs/c/yuan2.png") no-repeat; //标题后面的原点

    --title-bg-img-1: url("/src/assets/imgs/c/title.png") no-repeat;
    --title-bg-img-2: url("/src/assets/imgs/c/title.png") no-repeat;
    --title-bg-img-3: url("/src/assets/imgs/c/title.png") no-repeat;
    --title-bg-img-4: url("/src/assets/imgs/c/title.png") no-repeat;
    --title-bg-img-5: url("/src/assets/imgs/c/title.png") no-repeat;
    --title-bg-img-6: url("/src/assets/imgs/c/title.png") no-repeat;
    --title-bg-img-7: url("/src/assets/imgs/c/title.png") no-repeat;
    --title-bg-img-8: url("/src/assets/imgs/c/title.png") no-repeat;

    --info-bg-img1: url("@/assets/imgs/c/sy.png");
    --info-bg-img2: url("@/assets/imgs/bimgs/info-background.png");
    // 风险提示
    --sess-bg-img: url("@/assets/imgs/c/sess.png") no-repeat;
    --info-bg-img: url("@/assets/imgs/c/info.png") no-repeat;
    --border-line: #88a644;
    // 详情子标题左侧图标
    --title-child-bg-img1: url("@/assets/imgs/bimgs/zz.png") no-repeat;
    --title-child-bg-img2: url("@/assets/imgs/bimgs/gc.png") no-repeat;
    --title-child-bg-img3: url("@/assets/imgs/bimgs/jg.png") no-repeat;

    /* antd-mobile */
    --adm-button-border-radius: var(--border-radius);
    --adm-color-primary: var(--tab-color);
    --placeholder-color: var(--text-secondary);
    :global {
        .adm-input {
            --placeholder-color: var(--text-secondary);
        }
    }
}
