.collapseTable {
    --th-text-color: var(--text-main);
    padding: 0;
    :global {
        table {
            width: 100%;
            table-layout: fixed;
            border-spacing: 0 10px;
            padding-left: 5px;
        }
        th {
            color: var(--th-text-color);
            font-size: var(--text-md);
            padding-bottom: 6px;
            text-align: left;
            font-weight: normal;
        }
        td {
            color: var(--text-secondary);
            font-size: var(--text-sm);
        }
        .collapseTable__toggleIcon {
            cursor: pointer;
            text-align: right;
            color: var(--primary-color);
            font-size: 15px;
        }
    }
}
