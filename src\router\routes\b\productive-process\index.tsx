import React, { useEffect, useState } from "react";
import {
    create<PERSON><PERSON><PERSON><PERSON><PERSON>er,
    RouterProvider,
    Navigate,
    Outlet,
    useOutlet,
    useOutletContext,
    useLocation,
    useNavigate,
    Link,
    useMatches,
    useNavigationType,
} from "react-router-dom";
import { Badge } from "antd-mobile";

import NavBar from "@/components/nav-bar";
import TabBar, { RouteTabBar } from "@/components/tab-bar";
import CacheRouteContainer from "@/components/cache-route-container";

import type { RouteObject } from "react-router-dom";

import iconInbound from "@/assets/imgs/tabs/inbound.png";
import iconInboundSelected from "@/assets/imgs/tabs/inbound-selected.png";
import iconLog from "@/assets/imgs/tabs/log.png";
import iconLogSelected from "@/assets/imgs/tabs/log-selected.png";

import { ReactComponent as LogIcon } from "@/assets/imgs/tabs/svg/jiLu.svg";
import { ReactComponent as ProductiveIcon } from "@/assets/imgs/tabs/svg/guoCheng.svg";

import styles from "./index.module.less";

import BProductiveProcessEntry from "@/pages/b/productive-process/entry";
import BProductiveProcessLogList from "@/pages/b/productive-process/log-list";
import BProductiveProcessLogDetail from "@/pages/b/productive-process/log-detail";
import NoPermissionPage from "@/pages/403";

import useStore from "@/store";

const PackLayout = () => {
    const [pageTitle, setPageTitle] = useState("");
    const userMenuPermission = useStore((state) => state.userMenuPermission);

    const tabs = [
        {
            key: "entry",
            title: "过程录入",
            icon: (active: boolean) => (
                // active ? <img src={iconInboundSelected} /> : <img src={iconInbound} />,
                <ProductiveIcon
                    style={{
                        color: active ? "var(--tabs-primary-color)" : "#b2b2b2",
                    }}
                ></ProductiveIcon>
            ),
        },
        {
            key: "log",
            title: "记录查看",
            icon: (active: boolean) => (
                // active ? <img src={iconLogSelected} /> : <img src={iconLog} />,
                <LogIcon
                    style={{
                        color: active
                            ? "var( --tabs-primary-color)"
                            : "#b2b2b2",
                    }}
                ></LogIcon>
            ),
        },
    ];

    if (!userMenuPermission.find((item) => item.perms === "process")) {
        return (
            <div className={`_global_page ${styles.layout}`}>
                <NavBar>{pageTitle}</NavBar>
                <NoPermissionPage></NoPermissionPage>
            </div>
        );
    }

    return (
        <div className={`_global_page ${styles.layout}`}>
            <NavBar>{pageTitle}</NavBar>
            <div className="_global_pageScrollContent">
                <Outlet context={{ setPageTitle }}></Outlet>
            </div>
            <RouteTabBar
                tabs={tabs}
                prefix="/b/home/<USER>"
            ></RouteTabBar>
        </div>
    );
};

interface IPackPageWrapperProps {
    title?: string;
    children: React.ReactNode;
}
const PackPageWrapper = ({ children, title = "" }: IPackPageWrapperProps) => {
    const { setPageTitle } = useOutletContext<any>();
    useEffect(() => {
        setPageTitle(title);
    }, [title]);

    return children;
};

export default {
    path: "productive-process",
    element: <PackLayout></PackLayout>,
    children: [
        {
            index: true,
            element: <Navigate to="entry" replace></Navigate>,
        },
        {
            path: "entry",
            handle: {
                canBack: true,
            },
            element: (
                <PackPageWrapper title="生产过程">
                    <BProductiveProcessEntry></BProductiveProcessEntry>
                </PackPageWrapper>
            ),
        },
        {
            path: "log",
            // element: <CacheRouteContainer shouldCache={(location: any) => {
            //     return location.pathname === "/b/home/<USER>/log"
            // }}></CacheRouteContainer>,
            handle: {
                canBack: true,
            },
            children: [
                {
                    index: true,
                    element: (
                        <PackPageWrapper title="生产过程列表">
                            <BProductiveProcessLogList></BProductiveProcessLogList>
                        </PackPageWrapper>
                    ),
                },
                {
                    path: ":id",
                    handle: {
                        canBack: true,
                    },
                    element: (
                        <PackPageWrapper title="生产过程详情">
                            <BProductiveProcessLogDetail></BProductiveProcessLogDetail>
                        </PackPageWrapper>
                    ),
                },
            ],
        },
    ],
} as RouteObject;
