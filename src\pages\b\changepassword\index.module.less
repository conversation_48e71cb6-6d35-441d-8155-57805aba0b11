//out:false
.info_box {
    height: 100%;
    // background: #F1F4EE;
    background: var(--info-bg-img2)
        linear-gradient(180deg, #f1f1f1 0%, #ededed 100%);

    .back_img {
        height: 200px;
        background:
            var(--info-bg-img1) no-repeat center/100% 100%,
            linear-gradient(180deg, #f1f1f1 0%, #ededed 100%);

        .adm-nav-bar {
            padding-top: 13px;
        }
        color: var(--text-main-title);
    }

    .info_div {
        width: 90%;
        height: 320px;
        border-radius: 10px;
        box-shadow: 10px;
        background: #ffff;
        margin: 0 auto;
        position: relative;
        bottom: 120px;

        .flex {
            display: flex;
            align-items: center;
            padding: 10px 24px 15px;
            justify-content: space-between;

            .text {
                font-size: 12px;
            }
        }

        .eye {
            padding: 4px;
            cursor: pointer;
            font-size: var(--adm-font-size-10);
            color: rgba(0, 0, 0, 0.45);
        }

        .adm-form {
            --border-bottom: none !important;
            --border-top: none !important;
            padding-top: 20px;

            // .adm-list-item {
            //     .adm-list-item-content {
            //         box-shadow: inset 0 0 10px #E8EFDC;
            //         border: 1px solid #8EB249;
            //         border-radius: 8px;
            //         height: 30px;

            //         .adm-list-item-content-main {
            //             padding: 0;
            //         }
            //     }
            // }

            .adm-input-element {
                margin: 0 0 0 9px;
            }
        }

        .okbtn {
            cursor: pointer;
            width: 80%;
            height: 40px;
            display: flex;
            justify-content: center;
            //  border: 1px solid;
            background: linear-gradient(to bottom, #ffdf5f, #e27d0a);
            border-radius: 10px;

            // background:
            //     url("@/assets/imgs/bimgs/btn.png")no-repeat center/100% 100%,
            //     linear-gradient(180deg, #ffff 0%, #ffff 100%);
            span {
                display: inline-block;
                padding-bottom: 8px;
            }
        }

        .adm-button::before {
            background-color: transparent;

            // --background-color: var(--adm-color-text-dark-solid) transparent;
            span {
                // text-align: center;
                // color: white;
            }
        }

        .adm-form-item-child-inner {
            display: flex !important;
            align-items: flex-end !important;
        }
    }
}
