import React, { useState } from "react";
import {
    <PERSON><PERSON>,
    Input,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    DotLoa<PERSON>,
    Tabs,
    Image,
    Ellipsis,
} from "antd-mobile";
import classNames from "classnames";
import { DownOutline } from "antd-mobile-icons";

import styles from "./index.module.less";

import TraceBlockTitle from "@/pages/c/trace/components/trace-block-title";

import iconGroupTitleLeft from "@/assets/imgs/c/group-title-left.png";
import Gc from "@/assets/imgs/c/gc.png";
import Gg from "@/assets/imgs/c/jg.png";
import Zz from "@/assets/imgs/c/zz.png";
import Zz2 from "@/assets/imgs/bimgs/zz.png";
import Gc2 from "@/assets/imgs/bimgs/gc.png";
import Gg2 from "@/assets/imgs/bimgs/jg.png";
interface BlockItem {
    label?: React.ReactNode;
    value?: React.ReactNode;
    custom?: any;
}

type BlockItems = BlockItem[];

interface ITraceBlockProps {
    config: {
        custom?: any;
        title?: React.ReactNode;
        items: BlockItems;
    }[];
    title: React.ReactNode;
    custom?: any; // 自定义内容
}

export const isTraceBlockEmpty = (config: ITraceBlockProps["config"]) => {
    return (
        config.filter((configItem) => {
            return (
                configItem.items.filter((item) => item.value !== undefined)
                    .length > 0
            );
        }).length === 0
    );
};

function TraceBlock(props: ITraceBlockProps) {
    const { title, config } = props;
    console.log();

    const [isShowMore, setIsShowMore] = useState(false);

    const filteredConfig = config.filter((configItem) => {
        return (
            configItem.items.filter(
                (item) => item.value !== undefined || item.custom,
            ).length > 0
        );
    });
    console.log(filteredConfig, "filteredConfig", title);
    if (!config || filteredConfig.length === 0) {
        return null;
    }

    const renderConfig = () => {
        const renderItem = (items: any[]) => {
            return items.map((item, index) => {
                if (item.custom) {
                    return item.custom;
                }
                return (
                    item.value !== undefined && (
                        // 之前vw转换会导致Ellipsis异常，使用ignore禁止了转换。使用新的转换插件后暂时没有出现，没有使用ignore规则
                        <div className={styles.ignore_traceBlock__item}>
                            {item.label && (
                                <div className={styles.traceBlock__label}>
                                    {item.label == "种植过程" ? (
                                        <div
                                            style={{
                                                // height: 14,
                                                // paddingRight: "8px",
                                                marginTop: 1,
                                            }}
                                            className={styles.zhong}
                                            // src={Zz2}
                                        ></div>
                                    ) : (
                                        <img
                                            style={{
                                                height: 14,
                                                // paddingRight: "8px",
                                            }}
                                            src={""}
                                        />
                                    )}
                                    {item.label}
                                </div>
                            )}
                            <div className={styles.traceBlock__value}>
                                {item.value}
                            </div>
                        </div>
                    )
                );
            });
        };

        if (filteredConfig.length > 1) {
            return (
                <div className={styles.traceBlock__container}>
                    {/* {isShowMore ? */}
                    {filteredConfig.map((configItem) => {
                        if (configItem.custom) {
                            return configItem.custom;
                        }
                        return (
                            <div
                                className={classNames(
                                    styles.traceBlock__itemContainer,
                                    styles["traceBlock__itemContainer--more"],
                                )}
                            >
                                <div className={styles.traceBlock__groupTitle}>
                                    {/* <img
                                        style={{
                                            height: 14,
                                        }}
                                        src={
                                            configItem.title == "生产过程"
                                                ? Gc2
                                                : Gg2
                                        }
                                    /> */}
                                    {configItem.title == "生产过程" ? (
                                        <div className={styles.sheng}></div>
                                    ) : (
                                        <div
                                            className={styles.shengDetail}
                                        ></div>
                                    )}

                                    {configItem?.title}
                                </div>
                                {renderItem(configItem.items)}
                            </div>
                        );
                    })}
                    {/* // ) : (
                    //     <div className={styles.traceBlock__itemContainer}>
                    //         {renderItem(filteredConfig[0].items)}
                    //     </div>
                    // )}
                    {!isShowMore && (
                        <Button
                            block
                            className={styles.showMoreBtn}
                            onClick={() => {
                                setIsShowMore(true);
                            }}
                        >
                            查看更多
                            <DownOutline style={{ marginLeft: 8 }} />
                        </Button>
                    )} */}
                    {/* 渲染自定义内容 */}
                    {props.custom && props.custom}
                </div>
            );
        } else {
            const items = filteredConfig[0].items;
            return (
                <div
                    className={classNames(
                        styles.traceBlock__container,
                        styles.traceBlock__itemContainer,
                    )}
                >
                    {renderItem(filteredConfig[0].items)}
                    {/* 渲染自定义内容 */}
                    {props.custom && props.custom}
                </div>
            );
        }
    };

    return (
        <div className="info_detail">
            <TraceBlockTitle>{title}</TraceBlockTitle>
            {renderConfig()}
        </div>
    );
}

export default TraceBlock;
