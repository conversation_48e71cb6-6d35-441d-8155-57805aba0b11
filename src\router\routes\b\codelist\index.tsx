import React, { useEffect, useState } from "react";
import {
    create<PERSON><PERSON><PERSON><PERSON><PERSON>er,
    RouterProvider,
    Navigate,
    Outlet,
    useOutlet,
    useOutletContext,
    useLocation,
    useNavigate,
    Link,
} from "react-router-dom";
import { Badge } from "antd-mobile";

import NavBar from "@/components/nav-bar";
import TabBar, { RouteTabBar } from "@/components/tab-bar";
import CacheRouteContainer from "@/components/cache-route-container";

import type { RouteObject } from "react-router-dom";

import iconInbound from "@/assets/imgs/tabs/inbound.png";
import iconInboundSelected from "@/assets/imgs/tabs/inbound-selected.png";
import iconLog from "@/assets/imgs/tabs/log.png";
import iconLogSelected from "@/assets/imgs/tabs/log-selected.png";

import { ReactComponent as LogIcon } from "@/assets/imgs/tabs/svg/log.svg";
import { ReactComponent as InboundIcon } from "@/assets/imgs/tabs/svg/inbound.svg";

import { ReactComponent as PublicIcon } from "@/assets/qin/icon/public.svg";
import { ReactComponent as LogIconP } from "@/assets/qin/icon/log.svg";
// import { ReactComponent as InboundIcon } from "@/assets/imgs/tabs/svg/inbound.svg";

import styles from "./index.module.less";

import BScanInbound from "@/pages/b/codelist/scan-inbound";
import BInboundLogList from "@/pages/b/codelist/log-list";
import BInboundLogDetail from "@/pages/b/codelist/inbound-log-detail";
import BInboundLogEdit from "@/pages/b/codelist/inbound-log-detail";
import BReportErrorResult from "@/pages/b/report-error-result";
import NoPermissionPage from "@/pages/403";

import useStore from "@/store";
const PackLayout = () => {
    const [pageTitle, setPageTitle] = useState("");
    const userMenuPermission = useStore((state) => state.userMenuPermission);
    const navigate = useNavigate();
    const location = useLocation();
    const tabs = [
        {
            key: "scan",
            title: "绑定产品",
            icon: (active: boolean) => (
                // active ? <img src={iconInboundSelected} /> : <img src={iconInbound} />,
                <PublicIcon
                    style={{
                        color: active ? "var(--primary-color)" : "#e7e7e7",
                    }}
                ></PublicIcon>
            ),
        },
        {
            key: "log",
            title: "记录查看",
            icon: (active: boolean) => (
                // active ? <img src={iconLogSelected} /> : <img src={iconLog} />,
                <LogIconP
                    style={{
                        color: active ? "var(--primary-color)" : "#e7e7e7",
                    }}
                ></LogIconP>
            ),
        },
    ];

    if (!userMenuPermission.find((item) => item.perms === "code")) {
        return (
            <div className={`_global_page ${styles.layout}`}>
                <NavBar>{pageTitle}</NavBar>
                <NoPermissionPage></NoPermissionPage>
            </div>
        );
    }

    return (
        <div className={`_global_page ${styles.layout}`}>
            <NavBar
                onBack={() => {
                    if (
                        pageTitle === "溯源码管理" &&
                        location.search == "?step=form"
                    ) {
                        navigate("/b/home");
                    } else {
                        navigate(-1);
                    }
                }}
            >
                {pageTitle}
            </NavBar>
            <div className="_global_pageScrollContent">
                <Outlet context={{ setPageTitle }}></Outlet>
            </div>
            <RouteTabBar tabs={tabs} prefix="/b/home/<USER>"></RouteTabBar>
        </div>
    );
};

interface IPackPageWrapperProps {
    title?: string;
    children: React.ReactNode;
}
const PackPageWrapper = ({ children, title = "" }: IPackPageWrapperProps) => {
    const { setPageTitle } = useOutletContext<any>();
    useEffect(() => {
        setPageTitle(title);
    }, [title]);

    return children;
};

export default {
    path: "codelist",
    element: <PackLayout></PackLayout>,
    children: [
        {
            index: true,
            element: <Navigate to="scan" replace></Navigate>,
        },
        {
            path: "scan",
            element: <CacheRouteContainer></CacheRouteContainer>,
            children: [
                {
                    index: true,
                    element: (
                        <PackPageWrapper title="溯源码管理">
                            <BScanInbound></BScanInbound>
                        </PackPageWrapper>
                    ),
                },
                {
                    path: "result",
                    element: (
                        <PackPageWrapper title="溯源码管理">
                            <BReportErrorResult></BReportErrorResult>
                        </PackPageWrapper>
                    ),
                },
            ],
        },
        {
            path: "log",
            children: [
                {
                    index: true,
                    element: (
                        <PackPageWrapper title="溯源码列表">
                            <BInboundLogList></BInboundLogList>
                        </PackPageWrapper>
                    ),
                },
                {
                    path: ":id",
                    element: (
                        <PackPageWrapper title="溯源码详情">
                            <BInboundLogDetail></BInboundLogDetail>
                        </PackPageWrapper>
                    ),
                },
            ],
        },
    ],
} as RouteObject;
