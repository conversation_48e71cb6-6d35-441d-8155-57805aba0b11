if [ -z "$ssl_certificate" ]; then
  printf "\e[43mWARNING: 没有设置ssl证书\e[0m\n"
fi

if [ -z "$ssl_certificate_key" ]; then
  printf "\e[43mWARNING: 没有设置ssl秘钥\e[0m\n"
fi
if [ -z "$APISERVER" ]; then
  printf "\e[43mWARNING: 没有设置后端地址\e[0m\n"
fi

echo ${ssl_certificate} | base64 -d > /app/ssl/certificate.pem
echo ${ssl_certificate_key} | base64 -d > /app/ssl/private.key

echo '
server {
    listen      80;
    server_name 0.0.0.0;

    root /usr/share/nginx/html;
    index index.html index.htm;

    location / {
        try_files $uri /index.html;
    }
    location /apilk {
    rewrite ^/apilk/(.*)$ /$1 break;
    proxy_pass '${APISERVER}';
  }
    location /api {
        rewrite ^/api/(.*)$ /$1 break;
        proxy_pass '${APISERVER}';
    }

}
server {
    listen      443 ssl;
    server_name 0.0.0.0;

    ssl_certificate /app/ssl/certificate.pem;
    ssl_certificate_key /app/ssl/private.key;

    root /usr/share/nginx/html;
    index index.html index.htm;

    location / {
        try_files $uri /index.html;
    }
   location /apilk {
    rewrite ^/apilk/(.*)$ /$1 break;
    proxy_pass '${APISERVER}';
  }
    location /api {
        rewrite ^/api/(.*)$ /$1 break;
        proxy_pass '${APISERVER}';
    }
}
' >/etc/nginx/conf.d/default.conf
cat /etc/nginx/conf.d/default.conf

nginx -g 'daemon off;'

