.themeVariables {
    /* custom */
    --primary-color: #239066;
    --secondary-color: #f1faf1;
    --dangerous-color: #ff6161;
    --support-color: #d8d8d8;
    --home-bg: #ffffff;
    --page-bg-color: #fafafa;
    --page-block-color: #fff;
    --text-main: #333;
    --text-secondary: #909090;
    --text-main-title: #fff;
    --page-block-padding: 10px;
    --border-radius: 8px;
    --text-sm: 12px;
    --text-md: 14px;
    --text-lg: calc(var(--text-md) + 4px);
    --page-bg-img: url("@/assets/imgs/bimgs/backgroung2.png"); //
    --page-bg-img1: url("@/assets/imgs/bimgs/bhome2.png"); // 首页背景
    --btn-bg-img: url("@/assets/imgs/bimgs/btn2.png");
    --btn-bg-img-nui: url("@/assets/imgs/bimgs/homeBtn.png");
    --tab-color: #239066;
    --text-color: #4d4d4d;
    --tabs-primary-color: #239066;
    --tab-color-Bg: #f1fffa;
    --bg-color: #2390668a;
    --btn-bg-suss: rgba(128, 169, 50, 0.8);
    --btn-bg-info: rgba(255, 170, 0, 0.8);
    --title-bg-1: url("@/assets/imgs/c/yuan3.png") no-repeat; //标题后面的原点

    --title-bg-img-1: url("@/assets/imgs/bimgs/title/chan.png") no-repeat; //产品信息
    --title-bg-img-2: url("@/assets/imgs/bimgs/title/zhong.png") no-repeat; // 种植溯源
    --title-bg-img-3: url("@/assets/imgs/bimgs/title/sheng.png") no-repeat; // 生产溯源
    --title-bg-img-4: url("@/assets/imgs/bimgs/title/chan.png") no-repeat; // 产品说明
    --title-bg-img-5: url("@/assets/imgs/bimgs/title/chan.png") no-repeat; // 产地详情
    --title-bg-img-6: url("@/assets/imgs/bimgs/title/zhi.png") no-repeat; // 质检信息
    --title-bg-img-7: url("@/assets/imgs/bimgs/title/qi.png") no-repeat; // 企业基础信息
    --title-bg-img-8: url("@/assets/imgs/bimgs/title/sheng.png") no-repeat; // 生产商

    --info-bg-img1: url("@/assets/imgs/bimgs/grass2.png");
    --info-bg-img2: url("@/assets/imgs/bimgs/infoBackground.png");
    // 风险提示
    --sess-bg-img: url("@/assets/imgs/c/sess.png") no-repeat;
    --info-bg-img: url("@/assets/imgs/c/info.png") no-repeat;

    /* antd-mobile */
    --adm-button-border-radius: var(--border-radius);
    --adm-color-primary: var(--primary-color);
    --placeholder-color: var(--text-secondary);

    :global {
        .adm-input {
            --placeholder-color: var(--text-secondary);
        }
    }
}
