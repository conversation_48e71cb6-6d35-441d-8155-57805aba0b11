import { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
    Button,
    Input,
    TextArea,
    Picker,
    Grid,
    ActionSheet,
    Image,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
} from "antd-mobile-icons";
import { useRequest } from "ahooks";

import useStore, { useTheme } from "@/store";
import { hidePhoneNumber, showConfirmModal } from "@/utils";
import { bSideRequests } from "@/services";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";

import { ReactComponent as MenuProductiveIcon } from "@/assets/imgs/menus/productive.svg";
import { ReactComponent as MenuPackIcon } from "@/assets/imgs/menus/pack.svg";
import { ReactComponent as MenuInboundIcon } from "@/assets/imgs/menus/inbound.svg";
import { ReactComponent as MenuOutboundIcon } from "@/assets/imgs/menus/outbound.svg";
import { ReactComponent as CodeIcon } from "@/assets/imgs/menus/code.svg";
import { ReactComponent as MenuTransportationIcon } from "@/assets/imgs/menus/transportation.svg";
import { ReactComponent as MenuLogoutIcon } from "@/assets/imgs/menus/logout.svg";
import Ecode from "@/assets/imgs/bimgs/image.png";
import styles from "./index.module.less";
import { formatArrNull } from "@/utils/index";
import JiaGong from "@/assets/imgs/bimgs/jiankong.png";
import JiaGong2 from "@/assets/imgs/bimgs/jiankong2.png";
import TuiChu from "@/assets/imgs/bimgs/tuichu.png";
import TuiChu2 from "@/assets/imgs/bimgs/tuichu2.png";
import classNames from "classnames";
import Tx from "@/assets/imgs/bimgs/tx.png";
import Code from "@/assets/imgs/bimgs/code.png";

export default () => {
    const navigate = useNavigate();
    const logout = useStore((state) => state.logout);
    const userInfo = useStore<any>((state) => state.userInfo);
    sessionStorage.setItem("userInfo", JSON.stringify(userInfo));
    const userMenuPermission = useStore((state) => state.userMenuPermission);
    const [showcode, setShowcode] = useState(false);
    const [ImageUrl, setDownloadedImageUrl] = useState<any>();

    const logoutRequest = useRequest(bSideRequests.logout, {
        manual: true,
        onSuccess(res) {
            logout();
            sessionStorage.clear();
        },
    });

    const getMenu = () => {
        let menusRet: {
            label: string;
            icon: React.ReactNode;
            onClick: () => void;
        }[] = [];
        //接口请求到菜单栏数据，本次汤原大米没有其他流程只有生产过程
        const permissions = formatArrNull(userMenuPermission)
            .map((item) => item.perms)
            .filter((val: any) => val === "process" || "code");

        if (permissions.includes("process")) {
            menusRet.push({
                label: "生产过程",
                // icon: <MenuProductiveIcon></MenuProductiveIcon>,
                icon: <img src={JiaGong} />,
                onClick() {
                    navigate("productive-process");
                },
            });
        }
        if (permissions.includes("box")) {
            menusRet.push({
                label: "装箱",
                icon: <MenuPackIcon></MenuPackIcon>,
                onClick() {
                    navigate("pack");
                },
            });
        }
        if (permissions.includes("in")) {
            menusRet.push({
                label: "入库",
                icon: <MenuInboundIcon></MenuInboundIcon>,
                onClick() {
                    navigate("inbound");
                },
            });
        }
        if (permissions.includes("out")) {
            menusRet.push({
                label: "出库",
                icon: <MenuOutboundIcon></MenuOutboundIcon>,
                onClick() {
                    navigate("outbound");
                },
            });
        }
        if (permissions.includes("code")) {
            menusRet.push({
                label: "溯源码管理",
                icon: <CodeIcon></CodeIcon>,
                onClick() {
                    navigate("codelist");
                },
            });
        }
        if (permissions.includes("logistics")) {
            menusRet.push({
                label: "物流",
                icon: <MenuTransportationIcon></MenuTransportationIcon>,
                onClick() {
                    const handler = ActionSheet.show({
                        cancelText: "取消",
                        getContainer() {
                            return document.getElementById("root")!;
                        },
                        actions: [
                            {
                                text: "自行运输",
                                key: "copy",
                                onClick: () => {
                                    navigate("self-transportation");
                                    handler.close();
                                },
                            },
                            {
                                text: "委托运输",
                                key: "edit",
                                onClick: () => {
                                    navigate("entrusted-transportation");
                                    handler.close();
                                },
                            },
                        ],
                        onClose: () => {},
                    });
                },
            });
        }
        menusRet.push({
            label: "退出",
            // icon: <MenuLogoutIcon></MenuLogoutIcon>,
            icon: <img src={TuiChu} />,
            onClick() {
                showConfirmModal({
                    title: "确认退出",
                    onConfirm() {
                        logoutRequest.run();
                    },
                });
            },
        });

        return menusRet;
    };

    const menus = getMenu();
    const handledetail = () => {
        navigate("/b/personalinfo"); //
    };
    const handleshow = () => {
        navigate("/b/code"); //
        // setShowcode(true)
    };
    return (
        <div className={`_global_page ${styles.home}`}>
            <NavBar color={"bank"} backArrow={false}>
                首页
            </NavBar>
            <div
                className={classNames({
                    main: true,
                    none: showcode,
                    block: !showcode,
                })}
            >
                <div className="home__welcome">
                    <div className="text">
                        <div>欢迎！{userInfo?.userName || "-"}</div>
                        <div onClick={handleshow}>
                            <Image
                                className={classNames({
                                    // showHome?'none':'block'
                                    none:
                                        userInfo?.identity != 7 ? true : false,
                                })}
                                style={{
                                    maxWidth: 20,
                                }}
                                src={Ecode}
                            />
                        </div>
                    </div>
                    <div
                        onClick={handledetail}
                        style={{ cursor: "pointer", width: "100px" }}
                    >
                        {userInfo?.telephone
                            ? hidePhoneNumber(userInfo.telephone)
                            : "-"}
                    </div>
                </div>
                <div className="home__menu">
                    <Grid columns={3}>
                        {menus.map((item, index) => {
                            return (
                                <Grid.Item
                                    key={index}
                                    className="home__menu--item"
                                >
                                    <Button fill="none" onClick={item.onClick}>
                                        <div className="menu__item--icon">
                                            {item.icon}
                                        </div>
                                        {item.label}
                                    </Button>
                                </Grid.Item>
                            );
                        })}
                    </Grid>
                </div>
            </div>
        </div>
    );
};
