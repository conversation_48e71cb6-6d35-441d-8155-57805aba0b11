import { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
    Button,
    Input,
    TextArea,
    Picker,
    Toast,
    Image,
    SpinLoading,
} from "antd-mobile";
import {
    PhoneFill,
    LockFill,
    EyeInvisibleOutline,
    EyeOutline,
} from "antd-mobile-icons";
import { useRequest } from "ahooks";

import request from "@/services";

import { bSideRequests } from "@/services";
import { rsaEncrypt } from "@/utils";

import useStore from "@/store";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";

// import iconAccount from "@/assets/imgs/b-login-icon-account.png";
import iconAccount from "@/assets/imgs/b-login-icon-account1.png";
import iconAccount2 from "@/assets/imgs/b-login-icon-account2.png";
import iconPassword from "@/assets/imgs/b-login-icon-password.png";
// import iconSafe from "@/assets/imgs/b-login-icon-safe.png";
import iconSafe from "@/assets/imgs/b-login-icon-safe1.png";
import iconSafe2 from "@/assets/imgs/b-login-icon-safe2.png";
import mes from "@/assets/imgs/b-login-icon-mes.png";
import logoNew from "@/assets/imgs/logonew.png";

import styles from "./index.module.less";

export default () => {
    const inputRef = useRef(null);
    const navigate = useNavigate();
    const login = useStore((state) => state.login);

    const [visible, setVisible] = useState(false);
    const [loginForm] = Form.useForm();
    const initJson: any = {
        title1: "欢迎使用",
        title2: "中移链农产品溯源平台",
        title3: "中移链农产品溯源平台",
    };
    const [titleJson, setTitleJson] = useState(initJson);

    const loginRequest = useRequest(bSideRequests.login, {
        manual: true,
        onSuccess: async (res) => {
            const jwt = res.data.data;
            sessionStorage.setItem("jwt", jwt);
            // const infodata=await
            const infodata = bSideRequests.getUserInfo();
            infodata
                .then((result) => {
                    console.log("信息11111111111111111", result);
                    // if (result?.data?.data?.firstFlag === 0) {
                    //     navigate("/b/editpassword");
                    //     sessionStorage.setItem("userId", result.data.data.id);
                    //     sessionStorage.setItem("firstlogin", "1");

                    //     return;
                    // } else {
                    // if (jwt) {

                    login();
                    navigate("/b", {
                        replace: true,
                    });
                    // }
                    // }
                })
                .catch((error) => {
                    // 处理错误
                    console.error(error);
                });
            if (!jwt) {
                Toast.show({
                    content: "获取token失败",
                });
            }
        },
        onError(err) {
            setMessageText(err?.response?.data.message);
            loginForm.setFields([
                {
                    name: "verificationCode",
                    errors: [
                        <span style={{ display: "flex" }}>
                            {/* <ExclamationCircleFilled style={{ color: '#f3b63c' }} /> */}
                            <Image
                                className="mes"
                                style={{ width: 14 }}
                                src={mes}
                            ></Image>
                            <span style={{ color: "#757575" }}>
                                {" "}
                                &nbsp;
                                {err?.response?.data.message}
                            </span>
                        </span>,
                    ],
                },
            ]);
            // refreshGraphicVerificationCode();
        },
    });
    // 发送验证码
    const Verification = useRequest(bSideRequests.Verification, {
        manual: true,
        onSuccess(response: any) {
            // const res = response?.data;
            // console.log(response.code);
            if (response?.data.code === 200) {
                console.log("倒计时");
                loginForm.setFields([
                    {
                        name: "verificationCode",
                        errors: [],
                    },
                ]);
                startCountdown();
            }
        },
        onError(err) {
            setMessageText(err?.response?.data.message);
            loginForm.setFields([
                {
                    name: "verificationCode",
                    errors: [
                        <span style={{ display: "flex" }}>
                            {/* <ExclamationCircleFilled
                                style={{ color: "#f3b63c" }}
                            /> */}
                            <Image
                                className="mes"
                                style={{ width: 14 }}
                                src={mes}
                            ></Image>

                            <span style={{ color: "#757575" }}>
                                {" "}
                                &nbsp;
                                {err?.response?.data.message}
                            </span>
                        </span>,
                    ],
                },
            ]);
            // refreshGraphicVerificationCode();
        },
    });

    const loginTitleInfo = useRequest(bSideRequests.getTitle, {
        manual: true,
        onSuccess(response: any) {
            const res = response?.data;
            console.log(res);
            if (res.code === 200) {
                const json = res?.data[0];
                setTitleJson(json);
                return;
            }
        },
        onError() {
            refreshGraphicVerificationCode();
        },
    });

    useEffect(() => {
        loginTitleInfo.run({
            titleType: 1,
        });
    }, []);
    const xRequestIdRef = useRef(null);
    const [graphicVerificationCodeUrl, setGraphicVerificationCodeUrl] =
        useState<string | undefined>(undefined);
    // const getGraphicVerificationCodeRequest = useRequest(
    //     bSideRequests.getGraphicVerificationCode,
    //     {
    //         onSuccess(res) {
    //             const xRequestId = res.headers["x-request-id"];
    //             xRequestIdRef.current = xRequestId;
    //             const url = URL.createObjectURL(res.data);
    //             setGraphicVerificationCodeUrl(url);
    //         },
    //         onError(err: any) {
    //             const errBlob = err?.response?.data;
    //             if (errBlob?.type === "application/json") {
    //                 const reader = new FileReader();
    //                 reader.onload = function () {
    //                     const text = reader.result as string;
    //                     const json = JSON.parse(text);
    //                     const errMessage = json.message;
    //                     Toast.show(errMessage);
    //                 };
    //                 reader.readAsText(errBlob);
    //             } else {
    //                 Toast.show("获取验证码失败");
    //             }
    //         },
    //     },
    // );
    const refreshGraphicVerificationCode = () => {
        // getGraphicVerificationCodeRequest.refresh();
        loginForm.resetFields(["verificationCode"]);
    };

    const renderGraphicVerificationCode = () => {
        // if (getGraphicVerificationCodeRequest.loading) {
        //     return (
        //         <SpinLoading
        //             style={{
        //                 "--size": "20px",
        //             }}
        //         ></SpinLoading>
        //     );
        // }
        return (
            <Button
                size="small"
                type="button"
                className="visBtn"
                style={
                    countdown > 0
                        ? {
                              background: "var(--primary-color)",
                              color: "#ffffff !important",
                              border: "none",
                          }
                        : { border: "none" }
                }
                onClick={verificationClick}
                disabled={isDisabled || countdown > 0}
            >
                <span
                    style={{
                        fontSize: 16,
                        // color: countdown > 0 ? "#ffffff" : "#76ae55",
                    }}
                >
                    {countdown > 0 ? `重新发送${countdown}s` : verification}
                </span>
            </Button>
        );
    };
    const [countdown, setCountdown] = useState(0);
    const [verification, setVerification] = useState("发送验证码");
    const [isDisabled, setIsDisabled] = useState(true);
    const [messageText, setMessageText] = useState("请输入验证码");
    const [status, setStatus] = useState(false);
    const startCountdown = () => {
        setCountdown(60);
        setIsDisabled(true);
    };

    useEffect(() => {
        let intervalId: NodeJS.Timeout | undefined;

        if (countdown > 0) {
            intervalId = setInterval(() => {
                setCountdown((prevCountdown) => prevCountdown - 1);
                setVerification("重新发送");
            }, 1000);
        } else {
            if (verification == "重新发送") {
                loginForm.setFields([
                    {
                        name: "verificationCode",
                        errors: [],
                    },
                ]);
                setIsDisabled(false);
            }

            // setIsDisabled(false);
            // setMessageText("11111111");
        }

        // 清理定时器
        return () => clearInterval(intervalId);
    }, [countdown]);

    const verificationClick = async () => {
        console.log(loginForm.getFieldValue("phone"), "11111111");
        if (
            loginForm.getFieldValue("phone")?.length == 11 &&
            loginForm.getFieldValue("phone")
        ) {
            const pKRet = await request({
                method: "get",
                url: "/sys-config/getPublicKey",
                data: {},
            });

            const pK = pKRet?.data?.data || "";
            // loginForm.setFields([
            //     {
            //         name: "verificationCode",
            //         errors: [],
            //     },
            // ]);
            Verification.run({
                phoneNumber: await rsaEncrypt(
                    loginForm.getFieldValue("phone"),
                    pK,
                ),
                loginSource: 2,
            });
        } else {
            loginForm.getFieldValue("phone")
                ? ""
                : loginForm.setFields([
                      {
                          name: "phone",
                          errors: [<span>请输入手机号码</span>],
                      },
                  ]);
        }
        // 模拟发送验证码的逻辑
        console.log("Sending verification code...");
        // startCountdown();
    };
    // const renderGraphicVerificationCode = () => {
    //     if (getGraphicVerificationCodeRequest.loading) {
    //         return (
    //             <SpinLoading
    //                 style={{
    //                     "--size": "20px",
    //                 }}
    //             ></SpinLoading>
    //         );
    //     }
    //     return (
    //         <Image
    //             style={{
    //                 maxWidth: 150,
    //             }}
    //             onClick={() => {
    //                 refreshGraphicVerificationCode();
    //             }}
    //             src={graphicVerificationCodeUrl}
    //             alt="验证码"
    //         />
    //     );
    // };
    const [value1, setValue1] = useState("");

    const handleInput = (e: any) => {
        // 使用正则表达式匹配数字，只允许数字通过
        const onlyNumbers = e.replace(/[^0-9]/g, "");
        console.log(onlyNumbers, "onlyNumbers");
        loginForm.setFieldsValue({
            verificationCode: onlyNumbers,
        });
        // setValue1(onlyNumbers);
        // e = onlyNumbers; // 直接修改输入框的值
    };
    const phoneFun = (e: any) => {
        // 使用正则表达式匹配数字，只允许数字通过
        if (!e) {
            setIsDisabled(true);
        }
        // setValue1(onlyNumbers);
        // e = onlyNumbers; // 直接修改输入框的值
    };

    return (
        <div className={`_global_page ${styles.login}`}>
            <div className="main login_input--style">
                <div className={styles.leftImgFont}>
                    {/* <p className="pLine"></p> */}
                    <p>{titleJson.title1}</p>
                    <p>{titleJson.title2}</p>
                </div>
                <div className="title">
                    {titleJson.title3}
                    <img className={styles.titleIcon} src={logoNew} alt="" />
                </div>
                <Form
                    form={loginForm}
                    layout="horizontal"
                    onFinishFailed={(failInfo) => {
                        const errorMsg =
                            failInfo?.errorFields?.[0]?.errors?.[0];

                        // Toast.show(errorMsg);
                    }}
                    onFinish={async (values) => {
                        console.log(values);
                        const pKRet = await request({
                            method: "get",
                            url: "/sys-config/getPublicKey",
                            data: {},
                        });

                        const pK = pKRet?.data?.data || "";
                        // debugger
                        loginRequest.run({
                            phoneNumber: await rsaEncrypt(values.phone, pK),
                            // password: await rsaEncrypt(values.password, pK),
                            loginSource: 2,
                            smsCode: values.verificationCode,
                            xRequestId: xRequestIdRef.current,
                            // t:pK // 查看public Key
                        });
                    }}
                >
                    <div className="formArea ">
                        <div className="formItem">
                            <Image
                                className="formItem__label"
                                src={iconAccount}
                            ></Image>
                            <Form.Item
                                name="phone"
                                noStyle
                                validateFirst
                                rules={[
                                    {
                                        required: true,
                                        // message: "请输入手机号码",
                                        message: "请输入手机号码",
                                    },
                                    // {
                                    //     pattern: /^[1][3,4,5,6.7,8,9][0-9]{9}$/,
                                    //     message: "请输入正确的手机号",
                                    // },
                                    () => ({
                                        validator: (_, value, callback) => {
                                            const regExp = new RegExp(
                                                /^[1][3,4,5,6.7,8,9][0-9]{9}$/,
                                            );
                                            const verify = regExp.test(value);
                                            if (!value) {
                                                setIsDisabled(true);
                                                callback("请输入手机号码");
                                            } else if (verify === false) {
                                                setStatus(false);
                                                setIsDisabled(true);
                                                callback(
                                                    "输入手机号格式错误，请重新输入",
                                                );
                                            } else {
                                                setIsDisabled(false);
                                                setStatus(true);
                                                callback();
                                            }
                                        },
                                    }),
                                ]}
                            >
                                <Input
                                    className="formItem__input"
                                    type="tel"
                                    placeholder="请输入手机号码"
                                    onChange={phoneFun}
                                    maxLength={11}
                                />
                            </Form.Item>
                            <Form.Item noStyle shouldUpdate>
                                {() => {
                                    const { errors = [] } =
                                        loginForm
                                            .getFieldsError()
                                            .find((item) => {
                                                return item.name[0] === "phone";
                                            }) || {};
                                    return (
                                        errors.length > 0 && (
                                            <div className="errInfo">
                                                <div className="formItem__label"></div>
                                                {errors[0]}
                                            </div>
                                        )
                                    );
                                }}
                            </Form.Item>
                        </div>
                        {/* <div className="formItem">
                            <Image
                                className="formItem__label"
                                src={iconPassword}
                            ></Image>
                            <Form.Item
                                name="password"
                                noStyle
                                rules={[
                                    { required: true, message: "密码不能为空" },
                                ]}
                            >
                                <Input
                                    className="formItem__input"
                                    type={visible ? "text" : "password"}
                                    placeholder="登录密码"
                                />
                            </Form.Item>
                            <div className="eye">
                                {!visible ? (
                                    <EyeInvisibleOutline
                                        onClick={() => setVisible(true)}
                                    />
                                ) : (
                                    <EyeOutline
                                        onClick={() => setVisible(false)}
                                    />
                                )}
                            </div>
                            <Form.Item noStyle shouldUpdate>
                                {() => {
                                    const { errors = [] } =
                                        loginForm
                                            .getFieldsError()
                                            .find((item) => {
                                                return (
                                                    item.name[0] === "password"
                                                );
                                            }) || {};
                                    return (
                                        errors.length > 0 && (
                                            <div className="errInfo">
                                                <div className="formItem__label"></div>
                                                {errors[0]}
                                            </div>
                                        )
                                    );
                                }}
                            </Form.Item>
                        </div> */}
                        <div className="formItem">
                            <Image
                                className="formItem__label"
                                src={iconSafe}
                            ></Image>
                            <Form.Item
                                name="verificationCode"
                                noStyle
                                rules={[
                                    {
                                        required: true,
                                        message: "请输入验证码",
                                        // (
                                        //     <span style={{ display: "flex" }}>
                                        //         {messageText ==
                                        //         "请输入验证码" ? (
                                        //             <>{messageText}</>
                                        //         ) : (
                                        //             <>
                                        //                 <Image
                                        //                     style={{
                                        //                         width: "13px",
                                        //                     }}
                                        //                     src={mes}
                                        //                 ></Image>
                                        //                 <span
                                        //                     style={{
                                        //                         color: "#757575",
                                        //                     }}
                                        //                 >
                                        //                     {" "}
                                        //                     {messageText}
                                        //                 </span>
                                        //             </>
                                        //         )}
                                        //     </span>
                                        // ),
                                    },
                                    // {
                                    //     pattern: /^[0-9]{6}$/,
                                    //     message: "短信验证码只能包含数字",
                                    // },
                                ]}
                            >
                                <Input
                                    className="formItem__input"
                                    maxLength={6}
                                    placeholder="请输入验证码"
                                    onChange={handleInput}
                                />
                            </Form.Item>
                            <div className="graphicVerificationCodeContainer">
                                {renderGraphicVerificationCode()}
                            </div>
                            <Form.Item noStyle shouldUpdate>
                                {() => {
                                    const { errors = [] } =
                                        loginForm
                                            .getFieldsError()
                                            .find((item) => {
                                                return (
                                                    item.name[0] ===
                                                    "verificationCode"
                                                );
                                            }) || {};

                                    return (
                                        errors.length > 0 && (
                                            <div className="errInfo">
                                                <div className="formItem__label"></div>
                                                {errors[0]}
                                            </div>
                                        )
                                    );
                                }}
                            </Form.Item>
                        </div>
                    </div>

                    {/* <div className="btn_bg"> */}
                    <Button
                        className="loginBtn"
                        loading={loginRequest.loading}
                        block
                        fill="none"
                        style={{ "--background-color": "transparent" }}
                        type="submit"
                        color="primary"
                    >
                        登录
                    </Button>

                    {/* </div> */}
                    {/* <Button
            className="loginBtn"
            loading={loginRequest.loading}
            block
            fill='none'
          // type="submit"
          // // color="primary"
          >
            登录
          </Button> */}
                </Form>
            </div>
        </div>
    );
};
