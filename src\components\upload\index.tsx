import React, { useState, use<PERSON>allback, useEffect } from "react";
import {
    Button,
    Input,
    TextArea,
    Picker,
    Space,
    Modal,
    ImageUploader,
} from "antd-mobile";
import { AddOutline } from "antd-mobile-icons";
import classNames from "classnames";
import axios from "axios";

import { getUploadUrl, getUploadUrlPost } from "@/services";
import { getVideoCover } from "@/utils";

import styles from "./index.module.less";

import type { ImageUploaderProps } from "antd-mobile";
import { decryptedUrl } from "@/utils/index";

interface UploadProps extends Omit<ImageUploaderProps, "upload"> {
    type?: "image" | "video";
    uploadBtnText?: string;
    help?: React.ReactNode;
}

const uploadFile = async (file: File) => {
    let uploadUrlRet = await getUploadUrl(file?.name);
    const uploadUrl = await decryptedUrl(uploadUrlRet.data);
    console.log("uploadUrl", uploadUrl);
    // await axios({
    //     method: "put",
    //     url: uploadUrl,
    //     data: file,
    // });

    let uploadRet = await getUploadUrlPost({
        data: file,
    });
    console.log(uploadRet.data);
    const newUrl = await decryptedUrl(uploadRet.data);
    // return uploadUrl.split("?")[0];
    return newUrl;
};

async function handleUpload(file: File, type: string) {
    return new Promise<any>(async (resolve, reject) => {
        try {
            if (type === "video") {
                const [fileUrl, coverUrl] = await Promise.all([
                    uploadFile(file),
                    getVideoCover(file),
                ]);
                resolve({
                    url: coverUrl,
                    previewUrl: URL.createObjectURL(file),
                    value: fileUrl,
                });
            } else {
                const url = await uploadFile(file);
                resolve({
                    url: URL.createObjectURL(file),
                    value: url,
                });
            }
        } catch (err) {
            reject(err);
        }
    });
}

export default function Upload(props: UploadProps) {
    const {
        type = "image",
        uploadBtnText,
        className,
        help,
        ...restProps
    } = props;

    const videoPreview = (videoUrl: string) => {
        const { close } = Modal.show({
            className: styles.videoPreviewModal,
            content: (
                <div
                    onClick={() => {
                        close();
                    }}
                    className="videoContainer"
                >
                    <video
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                        controls
                        src={videoUrl}
                        playsInline
                    ></video>
                </div>
            ),
            closeOnMaskClick: true,
        });
    };
    const previewConfig =
        type === "video"
            ? {
                  preview: false,
                  onPreview: (i: any, item: any) => {
                      videoPreview(item.previewUrl);
                  },
              }
            : {};

    const defaultUploadBtnTextMap = {
        image: "上传照片",
        video: "上传视频",
    };
    return (
        <div>
            <ImageUploader
                {...previewConfig}
                upload={(file) => {
                    return handleUpload(file, type);
                }}
                className={classNames(styles.imageUploader, className)}
                {...restProps}
            >
                <div className={styles.uploadBtn}>
                    <AddOutline className={styles.uploadBtn__addIcon} />
                    <div></div>
                    {uploadBtnText ?? defaultUploadBtnTextMap[type]}
                </div>
            </ImageUploader>
            {help && <div className={styles.uploadHelp}>{help}</div>}
        </div>
    );
}
