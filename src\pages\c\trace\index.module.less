//out:false
.cTrace {
    min-height: 100vh;
    // background:
    //     url("@/assets/imgs/c/syxx.png") no-repeat center top/100% url("@/assets/imgs/c/syxx.png") no-repeat center top/100%,
    //     #f2f2f2;
    background-image: var(--info-bg-img1), var(--info-bg-img2);
    background-size:
        100%,
        100% 100%;
    /* 定义每张图片的大小 */
    background-position:
        top left,
        bottom right;
    /* 定义每张图片的位置 */
    background-repeat: no-repeat, no-repeat;
    /* 禁止图片重复 */
    line-height: 1;

    .traceFunction {
        margin-top: 135px;
        display: flex;
        justify-content: center;

        &__info {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 7px 20px 7px 15px;
            border-radius: 14px;
            background: #ffffff;
            font-size: 12px;
            color: var(--primary-color);
        }

        &__icon {
            width: 15px;
        }
    }

    .main {
        padding: 45px 10px 20px 10px;

        .traceSearchInfo {
            width: 70%;
            margin: 0 auto;
            padding: 10px 24px 10px 24px;
            // background-color: rgba(255, 255, 255);
            text-align: center;
            background-color: var(--tab-color-Bg);

            @supports (backdrop-filter: blur(16px)) {
                backdrop-filter: blur(16px);
                background-color: var(--su-code);
            }

            border-radius: 8px;
            display: flex;
            flex-direction: column;
            gap: 18px;
            line-height: 1;

            &__item {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 12px;
                color: var(--primary-color);
            }

            &__label {
                width: 50px;
                text-align-last: justify;
            }

            &__value {
                font-size: 14px;
                font-weight: 500;
            }

            &__link {
                display: flex;
                align-items: center;
                text-decoration: none;
                color: #222222;
            }
        }
        .traceFunction__suss {
            margin: 0 auto;
            height: 55px;
            width: 210px;
            margin-bottom: 10px;
            border-radius: 4px;
            opacity: 1;
            background: var(--btn-bg-suss);
            box-sizing: border-box;
            border: 1px solid var(--border-line);
            display: flex; /* 使用flex布局 */
            align-items: center;
            box-sizing: border-box;
            align-items: center; /* 水平居中 */
            justify-content: center; /* 垂直居中 */
            text-align: center; /* 确保文本内容居中 */
            .traceFunction__img {
                width: 40px; /* 根据实际情况设置宽度 */
                height: 40px; /* 保持图片比例 */
                margin-right: 10px; /* 图片与文字之间留一些间距 */
                background: var(--sess-bg-img);
                background-size: 100% 100%;
            }

            // img {
            //     max-width: 100px; /* 根据实际情况设置宽度 */
            //     height: auto; /* 保持图片比例 */
            //     margin-right: 10px; /* 图片与文字之间留一些间距 */
            // }
            div {
                font-family: PingFang SC;
                font-size: 18px;
                font-weight: 500;
                line-height: 24px;
                text-align: center;
                text-transform: capitalize;
                letter-spacing: 0.015em;
                color: #ffffff;
            }
        }
        .traceFunction__info {
            margin: 0 auto;
            height: 55px;
            width: 250px;
            margin-bottom: 10px;
            border-radius: 4px;
            opacity: 1;
            background: var(--btn-bg-info);
            box-sizing: border-box;
            border: 1px solid #ffaa00;
            display: flex; /* 使用flex布局 */
            align-items: center;
            box-sizing: border-box;
            padding: 10px 0 10px 10px;
            .traceFunction__info_img {
                width: 40px; /* 根据实际情况设置宽度 */
                height: 40px; /* 保持图片比例 */
                margin-right: 10px; /* 图片与文字之间留一些间距 */
                background: var(--info-bg-img);
                background-size: 100% 100%;
            }
            // img {
            //     max-width: 100px; /* 根据实际情况设置宽度 */
            //     height: auto; /* 保持图片比例 */
            //     margin-right: 10px; /* 图片与文字之间留一些间距 */
            // }
            div {
                font-family: PingFang SC;
                font-size: 18px;
                font-weight: 500;
                color: #ffffff;
                .traceFunction__info_text {
                    font-size: 8px;
                    margin-top: 5px;
                }
            }
        }
    }

    .traceTabs {
        margin-top: 12px;
        --title-font-size: var(--text-md);
        --content-padding: 0;

        :global {
            .adm-tabs-header {
                background: var(--home-bg);
                border-radius: 8px;
                padding: 0 12px;

                .adm-tabs-tab-active {
                    color: var(--tabs-primary-color);
                    background: var(--title-bg-1);
                    // var(--page-bg-color);
                    background-size: 30%;
                    /* 定义每张图片的大小 */
                    background-position: center right;
                    /* 定义每张图片的位置 */
                    background-repeat: no-repeat;
                    /* 禁止图片重复 */
                    line-height: 1;
                }

                .adm-tabs-tab-line {
                    // background-color: #fff;
                    background-color: transparent;
                }
            }

            //   ._traceBlock__container_nne8r_1 {
            //     margin-top: 0;
            //     border-radius: none;
            //     background: none;
            // }
            .info_detail {
                margin: min(2.667vw, 12px);
                border-radius: 10px;
                background: var(--home-bg);
                // background-color: rgba(255, 255, 255, 0.5);
            }

            .adm-tabs-tab {
                padding: 13px 0;
            }

            ._traceBlock__container_nne8r_1 {
                opacity: 0.8;
                margin-top: 0;
                border-radius: none;
                background: none;
            }
        }
    }

    .introText {
        white-space: pre-wrap;
        font-size: 14px;
        line-height: 20px;
        color: #909090;
    }
}

.imgbox {
    width: 100%;
    height: 100%;
}
.block {
    background: var(--home-bg-bottom);
}
// .my_video_wrap{
//   position: absolute !important;
//   z-index: 88;
// }
.video_wrap {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 88;

    & > span {
        position: absolute;
        width: 34px;
        height: 31px;
        left: 0;
        top: 0;
        color: #fff;
        z-index: 88;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }
}
.homeBg {
    height: 75vh !important;
    border-radius: 100% 100% 100% 100% / 0% 0% 20% 20%;
}
.text {
    width: 100%;
    text-align: center;
    position: absolute;
    bottom: 20px;
    font-family: Source Han Sans;
    color: #a8a8a8;
    font-size: 12px;
}
.title {
    width: 30px;
    position: absolute;
    color: #fff;
    font-size: 40px;
    top: 6%;
    left: 45%;
}
.homeLogo {
    height: 30px;
    background: var(--home-bg-logo);
    background-size: contain;
    margin-bottom: 2vh;
    background-position: center; /* 添加此行来居中背景图 */
}
.traceSearchInfo__value {
    width: 100%;
    text-align: center;
}
.verticalTextContainer {
    display: flex;
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
    height: 75vh !important;
    position: absolute;
    color: #fff;
    font-size: 40px;
    top: 0;
    left: 41%;
}

.verticalText {
    writing-mode: vertical-rl; /* 垂直排列，从右到左 */
    text-orientation: mixed; /* 保持汉字直立 */
    letter-spacing: 5px; /* 调整字间距 */
    line-height: 1.5; /* 调整行高 */
    padding: 10px 0; /* 添加一些内边距 */
    white-space: nowrap; /* 确保文本不会换行 */
    overflow: visible; /* 确保内容不会被裁剪 */
}
// @media screen and (min-width: 980px) and (max-width: 1300px) {
//     .verticalTextContainer {
//         font-size: 2.4rem !important; /* 调整字体大小 */
//         left: 43%;
//     }
// }
/* 媒体查询：屏幕宽度在 800px 到 1000px 之间时，调整字体大小 */
@media screen and (min-width: 800px) and (max-width: 1000px) {
    .verticalTextContainer {
        font-size: 40px !important; /* 调整字体大小 */
    }
}

// @media screen and (min-width: 400px) and (max-width: 800px) {
//     .trace_btn {
//         width: 300px !important;
//     }
//     .verticalTextContainer {
//         font-size: 32px !important; /* 调整字体大小 */
//         top: 0;
//         left: 43%;
//     }
//     .text {
//         font-size: 12px;
//     }
//     .homeLogo {
//         height: 25px;
//     }
// }
