//out:false
:root {
    --list-item-extra-color: #000;
    // font-size: 14px;
}
.list {
    :global {
        .adm-list-item-content {
            gap: 5px;
        }
        .adm-list-item-content-arrow {
            color: var(--text-color);
        }
        .adm-list-item-content-main {
            color: var(--text-main);
            font-size: var(--text-md);
        }
        .adm-list-item-content-extra {
            color: var(--list-item-extra-color);
            font-size: var(--text-sm);
            word-break: break-all;
            // text-align: right;
            text-align: justify;
        }
        .adm-list-body {
            border: none;
        }
        .adm-list-body-inner {
            border-bottom: var(--border-inner);
        }
       
        .adm-list-item {
            border-top: var(--border-inner);
        }
        .adm-list-item-content {
            border-top: none;
        }
    }
}
