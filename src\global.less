//out:false
html,
body {
    width: 100%;
    height: 100%;
}

#root {
    transform: translateZ(0);
    height: 100%;
    > div > div {
        overflow: auto;
        /* 隐藏滚动条 */
    }

    > div > div::-webkit-scrollbar {
        width: 0px;
    }
}

._global_page {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

._global_pageScrollContent {
    flex: 1;
    overflow: auto;
}
._global_pageScrollContent > div::-webkit-scrollbar {
    width: 0px;
}
.adm-image-viewer-slides {
    overflow: hidden;
}

.wrap_input--style {
    .adm-list-item {
        .adm-list-item-content {
            box-shadow: inset 0 0 10px #e8efdc;
            border: 1px solid #8eb249;
            border-radius: 8px;
            height: 30px;

            .adm-list-item-content-main {
                padding: 0;
            }
        }
    }
}

.login_input--style {
    .formItem {
        box-shadow: inset 0 0 10px #e8efdc;
        border: 1px solid #8eb249;
        border-radius: 8px;
        height: 30px;

        &:first-child {
            margin-top: 5px;
        }
    }
}

.adm-button::before {
    background-color: transparent;
}

.block {
    display: block;
}

.none {
    display: none;
}

.imgbox {
    width: 100%;
    height: 100%;

    .adm-image {
        width: 100%;
        height: 100vh;
    }
}

.trace_btn {
    margin: 0 auto;
    cursor: pointer;
    width: 88%;
    height: 35px;
    display: flex;
    justify-content: center;
    color: #fff;
    //  border: 1px solid;
    // background: linear-gradient(to bottom, #FFDF5F, #E27D0A);
    border-radius: 10px;
    background-image: var(--btn-bg-img-nui);
    background-size: cover;
    background-position: 100% 18%;
    position: absolute;
    bottom: 15%;
    left: 23px;
    // background:
    //     url("@/assets/imgs/bimgs/btn.png")no-repeat center/100% 100%,
    //     linear-gradient(180deg, #ffff 0%, #ffff 100%);
    span {
        text-align: center;
        color: white;
    }
}
.tolink {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 260px;
    height: 30px;
    text-align: center;
    background: var(--btn-color-Bg);
    text-decoration: none;
    border-radius: 9px;
    margin: 0 auto;
    border: 1px solid;
    .tolinkText {
        font-size: 18px;
    }
}
.eye_style {
    display: flex;
    position: relative;

    .eye {
        position: absolute;
        right: 16px;
        top: 7px;
    }
}
/* 自定义对话框样式 */

.custom-dialog .adm-button:not(.adm-button-default).adm-button-fill-none {
    color: #000;
}
.custom-dialog
    .adm-dialog-footer
    .adm-dialog-action-row
    > .adm-dialog-button-bold {
    color: #3e803d;
}
.adm-list-default .adm-list-body {
    border-bottom: none !important;
}

@media screen and (min-width: 200px) and (max-width: 800px) {
    // .trace_btn {
    //     width: 80% !important;
    //     margin: 0 auto  ;
    // }

    .verticalTextContainer {
        border: 1px solid #000;
        font-size: 36px !important; /* 调整字体大小 */
        color: blue;
    }
}
.listItem {
    .adm-list-body-inner {
        border-bottom: none !important;
    }
}
