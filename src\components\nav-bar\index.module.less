.navBar {
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    // top: 10px;
    line-height: 44px;
    color: var(--text-main);
    // background: #fff;
    :global {
        .navBar__backArrow {
            cursor: pointer;
            position: absolute;
            left: 10px;
            padding: 10px;
            font-size: var(--text-lg);
            color: var(--text-main-title);
        }
        .navBar__title {
            font-size: var(--text-lg);
            color: var(--text-main-title);
        }
        .navBar__backArrow_c {
            cursor: pointer;
            position: absolute;
            left: 10px;
            padding: 10px;
            font-size: var(--text-lg);
            color: #000000;
        }
        .navBar__title_c {
            font-size: var(--text-lg);
            color: #000000;
        }
    }
}
