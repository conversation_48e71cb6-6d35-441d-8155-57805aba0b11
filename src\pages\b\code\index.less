//out:false
.code_info_box {
    height: 100%;
    // background: #F1F4EE;
    background:
        url("@/assets/imgs/bimgs/info-background.png") no-repeat center/100%
            100%,
        linear-gradient(180deg, #f1f1f1 0%, #ededed 100%);

    .back_img {
        height: 200px;
        background:
            url("@/assets/imgs/bimgs/personal-img.png") no-repeat center/100%
                100%,
            linear-gradient(180deg, #f1f1f1 0%, #ededed 100%);

        .adm-nav-bar {
            padding-top: 20px;
        }
        color: var(--text-main-title);
    }

    .info_div {
        width: 90%;
        // height: 320px;
        border-radius: 10px;
        background: #ffff;
        margin: 0 auto;
        position: relative;
        bottom: 90px;

        .avatar_div {
            display: flex;
            flex-direction: column;
            align-items: center;

            .avatar {
                width: 100px;
                height: 100px;

                img {
                    width: 100%;
                    height: 100%;
                }
            }

            .user_name {
                padding: 0 7px 0 7px;
            }
        }

        .info_text {
            padding: 0px 35px;

            .code_style {
                width: 250px;
                height: 250px;
                margin: 0 auto;
                // padding-top: 20px;

                img {
                    width: 100%;
                    height: 100%;
                }
            }

            .look_style {
                text-align: center;
                padding-bottom: 30px;
                // padding-top: 20px;
                margin: 0;
                a {
                    color: #386ef2;
                }
            }
        }
    }
}
