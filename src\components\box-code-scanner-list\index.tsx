import { useState, useCallback, useRef, useEffect, memo } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
    Button,
    Input,
    TextArea,
    Picker,
    Space,
    Popup,
    CheckList,
    Toast,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    UpOutline,
    DownOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import useUrlState from "@ahooksjs/use-url-state";
import { useImmer } from "use-immer";
import { useRequest, useSetState } from "ahooks";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import CollapseTable from "@/components/collapse-table";
import CheckListPopup from "@/components/check-list-popup";

import iconScanQrCode from "@/assets/imgs/scan-qrcode.png";
import { ReactComponent as SvgScanQrCode } from "@/assets/imgs/scan-qrcode.svg";

import {
    REPOSITORY_TYPE_CONSTANTS,
    MAX_SCAN_LENGTH,
    BOX_CODE_STATE_ERR,
} from "@/config";
import { useConfirmBlock, useScanQRCode } from "@/hooks";
import {
    bSideRequests,
    getPackCodeInfoByPackCode,
    getPackCodeInfoByPackCodeList,
    BoxCodeVerifyModule,
} from "@/services";
import { showToastLoading, showConfirmModal, requestDataSign } from "@/utils";

import usePropsImmerValue from "./use-props-immer-value";

import styles from "./index.module.less";

export type BoxCodeData = {
    id: number | string;
    code: string;
    name: string;
}[];

interface BoxCodeScannerProps {
    value?: BoxCodeData;
    defaultValue?: BoxCodeData;
    onChange?: (data: BoxCodeData) => void;
    module: BoxCodeVerifyModule;
}

const BoxCodeScanner = (props: BoxCodeScannerProps) => {
    const { onChange, value, defaultValue = [], module } = props;
    const { qrCodeScanElement, startScanQrCode, closeScanQrCode } =
        useScanQRCode();

    const [scannedBoxCodeInfo, setScannedBoxCodeInfo] =
        usePropsImmerValue<BoxCodeData>({ onChange, value, defaultValue });
    const scannedBoxCodeInfoRef = useRef(scannedBoxCodeInfo);
    scannedBoxCodeInfoRef.current = scannedBoxCodeInfo;
    const getScannedBoxCodeInfo = useCallback(
        () => scannedBoxCodeInfoRef.current,
        [],
    );

    const getPackCodeInfoByPackCodeRequest = useRequest(
        getPackCodeInfoByPackCodeList,
        {
            manual: true,
        },
    );

    const handleBoxCode = async (
        boxCodeStr: string,
        continueScan: () => void,
    ) => {
        const { close: closeLoading } = Toast.show({
            icon: "loading",
        });
        const continueScanWithMessage = (
            content: React.ReactNode,
            icon?: string,
        ) => {
            closeLoading();
            Toast.show({
                icon: icon,
                content: content,
                duration: 1000,
                afterClose() {
                    continueScan();
                },
            });
        };
        try {
            const boxInfoRet = await getPackCodeInfoByPackCodeRequest.runAsync({
                id: boxCodeStr,
            });

            const boxInfo = boxInfoRet?.data?.data || {};

            console.log(boxInfo, "boxInfo");

            if (boxInfo?.id) {
                const existIds = getScannedBoxCodeInfo().map((item) => item.id);
                if (existIds.includes(boxInfo.id)) {
                    continueScanWithMessage("该溯源码已添加");
                } else {
                    setScannedBoxCodeInfo((draft) => {
                        draft.push({
                            id: boxInfo.id,
                            name: "123123",
                            code: boxInfo.code,
                        });
                    });

                    continueScanWithMessage(
                        <div>
                            <div>{boxInfo.code}</div>
                            <div>扫描成功</div>
                        </div>,
                    );
                    console.log(getScannedBoxCodeInfo().length);

                    if (getScannedBoxCodeInfo().length + 1 >= MAX_SCAN_LENGTH) {
                        closeScanQrCode();
                    }
                }
            } else {
                continueScanWithMessage("获取溯源码信息失败");
            }
        } catch (err: any) {
            console.log(err, "err");
            if (err?.type === BOX_CODE_STATE_ERR) {
                continueScanWithMessage(
                    err?.message || "解析溯源码失败",
                    "fail",
                );
            } else {
                const errMsg = err?.response?.data?.message;
                continueScanWithMessage(errMsg || "解析溯源码失败", "fail");
            }
        }
    };

    return (
        <div className={styles.boxCodeScanner}>
            {scannedBoxCodeInfo.length > 0 ? (
                <CollapseTable
                    columns={[
                        {
                            title: "产品溯源码",
                            dataIndex: "code",
                            width: "40%",
                            // style: {
                            //     paddingLeft: "10px",
                            // },
                        },

                        {
                            width: "20%",
                            style: {
                                display: "flex",
                                justifyContent: "center",
                            },
                            render(_: any, record: any, index: any) {
                                return (
                                    <div
                                        onClick={() => {
                                            setScannedBoxCodeInfo((draft) => {
                                                draft.splice(index, 1);
                                            });
                                        }}
                                        className={styles.deleteBtn}
                                    >
                                        删除
                                    </div>
                                );
                            },
                        },
                    ]}
                    dataSource={scannedBoxCodeInfo}
                ></CollapseTable>
            ) : (
                ""
            )}

            {scannedBoxCodeInfo.length < MAX_SCAN_LENGTH && (
                <Button
                    loading={getPackCodeInfoByPackCodeRequest.loading}
                    className={styles.scanBtn}
                    color="primary"
                    fill="outline"
                    onClick={async () => {
                        startScanQrCode((boxCodeStr, continueScan) => {
                            const url = boxCodeStr;
                            const params = new URLSearchParams(
                                new URL(url).search,
                            );
                            const traceCodeId = params.get("traceCodeId");
                            handleBoxCode(traceCodeId, continueScan);
                        });
                    }}
                >
                    <SvgScanQrCode
                        className={styles.scanBtnIcon}
                    ></SvgScanQrCode>
                    <div>扫描产品溯源码</div>
                </Button>
            )}
            {qrCodeScanElement}
        </div>
    );
};

export default memo(BoxCodeScanner);
