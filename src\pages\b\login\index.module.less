//out:false
.login {
    display: flex;
    flex-direction: column;
    justify-content: center;
    background:
        url("@/assets/imgs/bimgs/background.png") no-repeat center/100% 100%,

        // url("@/assets/imgs/bimgs/background2.png") no-repeat center/100% 100%,
        // linear-gradient(180deg, #f1f1f1 0%, #ededed 100%);
    // background:
        // url("@/assets/imgs/b-login-bg.png") no-repeat center/100% 100%,
        linear-gradient(180deg, #f1f1f1 0%, #ededed 100%);

    :global {
        .main {
            display: flex;
            position: relative;
            flex-direction: column;
            margin: 0 29px;
            background:
                url("@/assets/imgs/bimgs/grass.png") no-repeat top/100% 166px,
                // #ffffff;
                // url("@/assets/imgs/bimgs/grass2.png") no-repeat top/100% 166px,
                #ffffff;
            // background:
            //     url("@/assets/imgs/b-login-main-bg-header.png") no-repeat
            //         top/100% 166px,
            //     #ffffff;
            padding: calc(166px + 24px) 20px 24px 20px;
            border-radius: 16px;
        }

        .eye {
            padding: 4px;
            cursor: pointer;
            font-size: var(--adm-font-size-10);
            color: rgba(0, 0, 0, 0.45);
        }

        .title {
            opacity: 1;
            color: #000;
            font-size: 18px;
            line-height: 36px;
            height: 36px;
            letter-spacing: 0px;
            margin-bottom: 18px;
            display: flex;
            align-items: center;
        }

        .formArea {
            display: flex;
            flex-direction: column;
            gap: 25px;

            .formItem {
                position: relative;
                border-radius: 0;
                // background: #f5f5fa;
                display: flex;
                align-items: center;
                // padding: 0 12px;
                // height: 40px;
                gap: 8px;
                border-left: none;
                border-right: none;
                border-top: none;
                box-shadow: none;
                // &__label {
                //     height: 13px;
                //     width: 13px;
                // }

                &__input {
                    flex: 1;
                    color: #909090;
                    --font-size: 14px;
                    line-height: 17px;
                    letter-spacing: 0px;

                    input {
                        height: 100%;

                        &::placeholder {
                            color: #909090;
                        }
                    }
                }
            }

            .errInfo {
                position: absolute;
                bottom: -20px;
                left: -8px;
                display: flex;
                align-items: center;
                // padding: 0 12px;
                gap: 8px;
                font-size: 12px;
                color: red;
            }
        }

        // .btn_bg {
        //     background:
        //         url("@/assets/imgs/bimgs/btn.png") no-repeat 100% 100% #ffffff;
        //     background-size: cover;
        //     /* 背景图片大小 */
        //     background-position: center;
        //     /* 背景图片位置 */
        //     background-repeat: no-repeat;
        //     /* 不重复 */
        //     // .adm-button::before {
        //     //     background-color: none !important; // 去掉点击时的背景颜色
        //     // }

        .loginBtn {
          height: 60px;
            margin-top: 25px;
            // border-radius: 10px;
            // padding: 13px 12px 15px 12px;
            font-size: 16px;
            line-height: 19px;
            letter-spacing: 0px;
            background-image: url("@/assets/imgs/bimgs/btn.png");
            background-size: 104% 106%;
            background-position: center 3px;
            background-repeat: no-repeat;
            span {
                color: white;
            }
        }
        .adm-button::before {
            background-color: transparent;
        }
        // }

        .graphicVerificationCodeContainer {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            .adm-image {
                max-height: 30px;
            }
        }
    }
}

.leftImgFont {
    position: absolute;
    top: 40px;
    left: 29px;

    p {
        color: #f1f1f1;
        font-size: 26px;
        margin: 0;
        line-height: 32px;
        height: 32px;
        margin-top: 10px;
        // font-weight: 600;
    }

    :global {
        .pLine {
            margin-bottom: 20px;
            width: 45%;
            height: 6px;
            background: #fff;
            border-radius: 6px;
        }
    }
}

.titleIcon {
    display: inline-block;
    margin-left: 10px;
    width: 70px;
    // position:'absolute';
    // right:'50px';
    // top:'50px';
}
// 验证码按钮

.btnVis {
    background-color: #000;
    color: #fff;
    border: none;
}
.visBtn {
    color: #bcbdbd;
    background: transparent;
    border: none;
}
.ant-btn[disabled],
.ant-btn[disabled]:hover,
.ant-btn[disabled]:focus,
.ant-btn[disabled]:active {
    background: transparent;
}
.visBtn:hover {
    color: #fff;
    background: #76ae55 !important;
    border-radius: 8px;
}
.adm-button:hover {
    border: none;
}
