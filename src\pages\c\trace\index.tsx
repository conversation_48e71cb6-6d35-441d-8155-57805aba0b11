import {
    useState,
    useEffect,
    useRef,
    useImper<PERSON><PERSON><PERSON><PERSON>,
    forwardRef,
} from "react";
import { Navigate, useNavigate, Link, useParams } from "react-router-dom";
import {
    Button,
    Input,
    TextArea,
    Picker,
    DotLoading,
    Tabs,
    Image,
    Ellipsis,
    ErrorBlock,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    RightOutline,
    CheckCircleFill,
} from "antd-mobile-icons";
import useUrlState from "@ahooksjs/use-url-state";
import { useRequest, useSetState } from "ahooks";
import dayjs from "dayjs";
import classNames from "classnames";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import PreviewImages from "@/components/preview-images";
import PreviewVideoOrigin from "@/components/preview-video";
import Text<PERSON>reaView from "@/components/text-area-view";

import <PERSON><PERSON><PERSON>, { isTraceBlockEmpty } from "./components/trace-block";
import iconWarning from "@/assets/imgs/c/icon-warning.png";
import bg from "@/assets/qin/homebg.png";
import suss from "@/assets/imgs/c/sess.svg";
import Info from "@/assets/imgs/c/info.png";
import { cSideRequests } from "@/services";
import {
    TRANSPORTATION_STATE_CONSTANTS,
    TRANSPORTATION_TYPE_CONSTANTS,
} from "@/config";

import styles from "./index.module.less";
import { decryptedUrl, isArrayArr } from "@/utils/index";
import { log } from "console";

import CSvideo from "../../../assets/imgs/video/videoa.mp4";
import homeLogo from "@/assets/qin/logo.png";
import useStore, { useTheme } from "@/store";
enum TraceTabsKey {
    trace = "trace",
    product = "product",
    inspection = "inspection",
    enterprise = "enterprise",
}
const PreviewVideo = forwardRef(PreviewVideoOrigin);

const Trace = () => {
    const navigate = useNavigate();

    const [urlState, setUrlState] = useUrlState<{
        traceCodeId?: string;
    }>();
    const [traceTabsActiveKey, setTraceTabsActiveKey] = useState<TraceTabsKey>(
        TraceTabsKey.trace,
    );
    const [promotionPicData, setPromotionPicData] = useState<any>();
    const [video, setVideo] = useState<any>();

    const [processDecImg, setProcessDecImg] = useState<any>([]);
    const [processDecVideo, setProcessDecVideo] = useState<any>([]);
    const [inspectionReportDec, setInspectionReportDec] = useState<any>();
    const [orgBasicQualification, setOrgBasicQualification] = useState<any>();
    const [orgBriefImg, setOrgBriefImg] = useState<any>();
    const [orgBriefVideo, setOrgBriefVideo] = useState<any>();

    const [pbVideo, setPbVideo] = useState<any>();
    const [pbImg, setPbImg] = useState<any>();
    const [pbApt, setPbApt] = useState<any>();
    //
    const [showHome, setShowHome] = useState(false);
    const [homeImg, setHomeImg] = useState<any>([]);
    const [videoSrc, setVideoSrc] = useState("");
    const [showVideo, setShowVideo] = useState(true);
    const [showAudio, setAudio] = useState(false);
    const AudioRef = useRef<any>(null);
    const VideoRef = useRef<any>(null);
    console.log("新增注释看提交");
    const changeTheme = useTheme((state) => state.changeTheme);
    const traceCodeDetailRequest = useRequest(
        () => {
            if (!urlState.traceCodeId) {
                navigate("error?msg=找不到溯源码ID", {
                    replace: true,
                });
                return Promise.reject();
            }
            return cSideRequests.getTraceDataByTraceCodeId(
                urlState.traceCodeId,
            );
        },
        {
            async onSuccess(res: any) {
                console.log("sssswwww===111---2", res);
                if (!res.data?.data?.product?.productName) {
                    navigate("error?msg=溯源码不存在", {
                        replace: true,
                    });
                }
                if (res?.data?.data?.themeType == 2) {
                    changeTheme("gray");
                } else {
                    changeTheme("blue");
                }
                const result = res?.data?.data?.process;

                let videosrc = decryptedUrl(res.data.data.openScreenVideo);
                //  console.log('video11111111111111111111111111111111111111111',videosrc);
                videosrc
                    .then((result) => {
                        // 设置视频路径

                        setVideoSrc(result);
                    })
                    .catch((error) => {
                        // 处理错误
                        console.error(error);
                    });
                // decryptedUrl(res.data.data.openScreenVideo).then(r => {
                //   // 设置视频路径
                //   setVideoSrc(r)
                //   console.log('video11111111111111111111111111111111111111111',res.data.data.openScreenVideo);

                // })

                let list = [];
                const allocationImg = decryptedUrl(res.data.data.openScreenImg);
                // const itemArr = await Promise.all(isArrayArr(item?.processImg)?.map((val: string) => decryptedUrl(val)))
                // console.log('img11111111111111111111111111111111111111111', allocationImg);
                allocationImg
                    .then((result) => {
                        setHomeImg(result);
                    })
                    .catch((error) => {
                        // 处理错误
                        console.error(error);
                    });
                // list.push(allocationImg)
                // console.log('img2222222222222222222222222222222222222', list);

                const arrayDataProcessImg = await Promise.all(
                    isArrayArr(result)?.map(async (item: any) => {
                        let arr = [];
                        if (
                            Array.isArray(item?.processImg) &&
                            item?.processImg?.length > 0
                        ) {
                            const itemArr = await Promise.all(
                                isArrayArr(item?.processImg)?.map(
                                    (val: string) => decryptedUrl(val),
                                ),
                            );
                            console.log(itemArr);
                            arr.push(...itemArr);
                        }
                        return arr;
                    }),
                );
                console.log("pp0000");
                setProcessDecImg(arrayDataProcessImg);

                console.log("pp1111111");
                const arrayDataProcessVideo = await Promise.all(
                    isArrayArr(result)?.map((item: any) => {
                        console.log("item?.processVideo", item?.processVideo);
                        if (item?.processVideo)
                            return decryptedUrl(item?.processVideo);
                        return null;
                    }),
                );
                setProcessDecVideo(arrayDataProcessVideo);
                // console.log('aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa', arrayDataProcessVideo);
            },
            onError(ret: any) {
                const errMsg = ret?.response?.data?.message || "请求失败";
                navigate(`error?msg=${errMsg}`);
            },
        },
    );
    const traceCodeDetail = traceCodeDetailRequest.data?.data?.data || {};

    const traceCodeProductDetailRequest = useRequest(
        () => {
            if (!urlState.traceCodeId) {
                return Promise.reject();
            }
            return cSideRequests.getProductTraceDataByTraceCodeId(
                urlState.traceCodeId,
            );
        },
        {
            async onSuccess(res: any) {
                console.log("ssss===", res);
                const result = res?.data?.data?.productBrief;
                if (result?.productVideo) {
                    const videos = await decryptedUrl(result?.productVideo);
                    setVideo(videos);
                }
                if (result?.productImg) {
                    const arrayData = await Promise.all(
                        isArrayArr(result?.productImg)?.map((item: any) => {
                            return decryptedUrl(item);
                        }),
                    );
                    // const image = await decryptedUrl(result?.productImg)
                    console.log(
                        "adscsdcffvfdvdavfvcfv122222313131312312332eedscsdcsdcsdc",
                        arrayData,
                    );

                    setPromotionPicData(arrayData);
                }
                const placebrief = res?.data?.data?.placeBrief;
                if (placebrief?.placeVideo) {
                    const pbvideos = await decryptedUrl(placebrief?.placeVideo);
                    setPbVideo(pbvideos);
                }
                const pbImgs = await Promise.all(
                    isArrayArr(placebrief?.placeImg)?.map((item: any) => {
                        return decryptedUrl(item);
                    }),
                );
                //const image = await decryptedUrl(result?.productImg)

                setPbImg(pbImgs);

                const placebasic = res?.data?.data?.placeBasic;
                const pbApts = await decryptedUrl(placebasic?.placeAptitude);
                setPbApt(pbApts);
            },
        },
    );
    const traceCodeProductDetail =
        traceCodeProductDetailRequest.data?.data?.data || {};

    const traceCodeInspectionDetailRequest = useRequest(
        () => {
            if (!urlState.traceCodeId) {
                return Promise.reject();
            }
            return cSideRequests.getInspectionTraceDataByTraceCodeId(
                urlState.traceCodeId,
            );
        },
        {
            async onSuccess(res: any) {
                const result = res?.data?.data;
                const arrayData = await Promise.all(
                    isArrayArr(result)?.map((item: any) => {
                        return decryptedUrl(item.inspectionReport);
                    }),
                );
                setInspectionReportDec(arrayData);
            },
        },
    );
    const traceCodeInspectionDetail =
        traceCodeInspectionDetailRequest.data?.data?.data || [];

    const traceCodeOrgDetailRequest = useRequest(
        () => {
            if (!urlState.traceCodeId) {
                return Promise.reject();
            }
            return cSideRequests.getOrgTraceDataByTraceCodeId(
                urlState.traceCodeId,
            );
        },
        {
            async onSuccess(res: any) {
                console.log("sorgp=", res);
                const result = res?.data?.data;
                const orgBasicRes = result?.orgBasic;
                const orgBriefRes = result?.orgBrief;
                console.log("orgssaaa", result);

                if (orgBasicRes?.qualification) {
                    const qualification = await decryptedUrl(
                        orgBasicRes?.qualification,
                    );
                    setOrgBasicQualification(qualification);
                }
                if (orgBriefRes?.picture) {
                    const arrayData = await Promise.all(
                        isArrayArr(orgBriefRes?.picture)?.map((item: any) => {
                            return decryptedUrl(item);
                        }),
                    );
                    setOrgBriefImg(arrayData);
                }
                const orgBriefVideo =
                    (await decryptedUrl(orgBriefRes?.video)) || "";
                setOrgBriefVideo(orgBriefVideo);
                //console.log('weweppp--setOrgBriefImg000', arrayData, qualification)
            },
        },
    );
    const traceCodeOrgDetail = traceCodeOrgDetailRequest.data?.data?.data || [];

    // if (!urlState.traceCodeId) {
    //     return <Navigate to="error?msg=找不到溯源码ID" replace></Navigate>
    // }

    if (traceCodeDetailRequest.loading) {
        return (
            <div
                style={{
                    paddingTop: "200px",
                    textAlign: "center",
                    color: "var(--adm-color-weak)",
                }}
            >
                <div style={{ fontSize: 24, marginBottom: 24 }}>
                    <DotLoading />
                </div>
                正在加载数据
            </div>
        );
    }
    // 溯源信息
    const renderTraceTabInfo = () => {
        // 产品信息
        const productInfo = traceCodeDetail?.product || {};
        const productDetailItems = [
            {
                label: "产品名称",
                value: productInfo.productName,
            },
            {
                label: "产品品类",
                value: productInfo.productCategory,
            },
            {
                label: "保质期",
                value: productInfo.expirationDate,
            },
            // {
            //     label: "产品编码",
            //     value: productInfo.productCode,
            // },
            {
                label: "生产许可证编号",
                value: productInfo.productionLicense,
            },
            {
                label: "产品执行标准",
                value: productInfo.executiveStandard,
            },
            {
                label: "产品单位",
                value: productInfo.productUnit,
            },
            {
                label: "产品规格",
                value: productInfo.specification,
            },
            {
                label: "配料",
                value: productInfo.ingredient,
            },
            // {
            //     custom: productInfo.productTransId && (
            //         <Link
            //             className="tolink"
            //             to={`certificate/${productInfo.productTransId}`}
            //         >
            //             {" "}
            //             <CheckCircleFill fontSize={18} /> 中移链溯源认证通过，点击了解更多
            //         </Link>
            //     ),
            //     //  <div><button >该信息已上链</button></div>
            // },
            // {
            //     label: "产品合格证明",
            //     value: productInfo.productAptitude && (
            //         <PreviewImages
            //             images={[productInfo.productAptitude]}
            //         ></PreviewImages>
            //     ),
            // },
            // {
            //   label: "链上哈希",
            //   value: productInfo.productTransId && (
            //     <Link
            //       style={{}}
            //       to={`certificate/${productInfo.productTransId}`}
            //     >
            //       <Ellipsis
            //         content={productInfo.productTransId}
            //       ></Ellipsis>
            //     </Link>
            //   ),
            // },
        ];
        // 种植溯源
        const plantInfo = traceCodeDetail?.landPlantInfo || {};
        const plantDetailItems = [
            { label: "种植过程", value: "" },
            {
                label: "农作物类型",
                value: plantInfo.plantName,
            },
            {
                label: "播种时间",
                value:
                    plantInfo.sowTime &&
                    dayjs(plantInfo.sowTime).format("YYYY-MM-DD HH:mm:ss"),
            },
            {
                label: "收割时间",
                value:
                    plantInfo.harvestTime &&
                    dayjs(plantInfo.harvestTime).format("YYYY-MM-DD HH:mm:ss"),
            },
            {
                label: "种植地块",
                value: plantInfo.landName,
            },
            {
                label: "地块产量",
                value: plantInfo.harvestNum
                    ? plantInfo.harvestNum + "吨"
                    : plantInfo.harvestNum,
            },
        ];

        //
        const materialInfo = traceCodeDetail?.material || [];
        const materialConfig = materialInfo.map((materialItem: any) => {
            return {
                title: "原料详情",
                items: [
                    {
                        label: "原料名称",
                        value: materialItem.materialName,
                    },
                    {
                        label: "原料采购批次",
                        value: materialItem.purchaseBatch,
                    },
                    {
                        label: "生产日期",
                        value:
                            materialItem.productionDate &&
                            dayjs(materialItem.productionDate).format(
                                "YYYY.MM.DD HH:mm:ss",
                            ),
                    },
                    {
                        label: "保质期",
                        value: materialItem.expiration,
                    },
                    {
                        label: "原料数量",
                        value: materialItem.count,
                    },
                    {
                        label: "原料规格",
                        value: materialItem.specification,
                    },
                    {
                        label: "原料合格证明",
                        value: materialItem.certificate && (
                            <PreviewImages
                                images={[materialItem.certificate]}
                            ></PreviewImages>
                        ),
                    },
                    {
                        label: "原料图片",
                        value: materialItem.materialImg && (
                            <PreviewImages
                                images={[materialItem.materialImg]}
                            ></PreviewImages>
                        ),
                    },
                    {
                        label: "供应商",
                        value: materialItem.supplier,
                    },
                    {
                        label: "链上哈希",
                        value: materialItem.purchaseTransId && (
                            <Link
                                style={{}}
                                to={`certificate/${materialItem.purchaseTransId}`}
                            >
                                <Ellipsis
                                    content={materialItem.purchaseTransId}
                                ></Ellipsis>
                            </Link>
                        ),
                    },
                ],
            };
        });

        //注释掉
        const processInfo = traceCodeDetail?.process || [];
        // 动态赋值
        const processConfig = processInfo.map(
            (processItem: any, index: number) => {
                // const processConfig = processInfo.map((processItem: any) => {
                return {
                    title: "生产过程",
                    items: [
                        {
                            label: "生产过程名称",
                            value: processItem.processName,
                        },
                        {
                            label: "生产过程说明",
                            value: processItem.processInstructions && (
                                <TextAreaView
                                    content={processItem.processInstructions}
                                ></TextAreaView>
                            ),
                        },
                        {
                            label: "生产过程图片",
                            value: processItem.processImg &&
                                processDecImg &&
                                processDecImg[index] &&
                                processDecImg[index].length > 0 && (
                                    <PreviewImages
                                        images={processDecImg[index]}
                                    ></PreviewImages>
                                    // <PreviewImages
                                    //     images={processItem.processImg}
                                    // ></PreviewImages>
                                ),
                        },
                        {
                            label: "生产过程视频",
                            value: processItem.processVideo &&
                                processDecVideo && (
                                    // <PreviewVideo
                                    //     src={processItem.processVideo}
                                    // ></PreviewVideo>
                                    <PreviewVideo
                                        preview={false}
                                        src={processDecVideo[index]}
                                        // src={CSvideo}
                                    ></PreviewVideo>
                                ),
                        },
                        // {
                        //   label: "链上哈希",
                        //   value: processItem.processTransId && (
                        //     <Link
                        //       style={{}}
                        //       to={`certificate/${processItem.processTransId}`}
                        //     >
                        //       <Ellipsis
                        //         content={processItem.processTransId}
                        //       ></Ellipsis>
                        //     </Link>
                        //   ),
                        // },

                        // {
                        //     custom: processItem.processTransId && (
                        //         <Link
                        //             className="tolink"
                        //             to={`certificate/${processItem.processTransId}`}
                        //         >
                        //             <CheckCircleFill fontSize={18} />{" "}
                        //             中移链溯源认证通过，点击了解更多
                        //         </Link>
                        //     ),
                        // },
                    ],
                };
            },
        );

        const productionInfo = traceCodeDetail?.production || {};
        const unit =
            productionInfo?.unit == 1
                ? "袋"
                : productionInfo?.unit == 2
                ? "盒"
                : productionInfo?.unit == 3
                ? "个"
                : productionInfo?.unit == 4
                ? "把"
                : productionInfo?.unit == 5
                ? "台"
                : productionInfo?.unit == 6
                ? "床"
                : "";
        processConfig.push({
            title: "生产加工详情",
            items: [
                {
                    label: "批次号",
                    value: productionInfo.productionBatch,
                },
                {
                    label: "数量",
                    value: productionInfo.amount
                        ? productionInfo.amount + unit
                        : productionInfo.amount,
                },
                {
                    label: "生产线",
                    value: productionInfo.line,
                },

                {
                    label: "种植户",
                    value: productionInfo.grower,
                },
                {
                    label: "生产班次",
                    value: productionInfo.shift,
                },
                {
                    custom: productionInfo.productionTransId && (
                        <Link
                            className="tolink"
                            to={`certificate/${productionInfo.productionTransId}`}
                        >
                            <CheckCircleFill className="tolinkText" />
                            &nbsp; 中移链溯源认证通过，点击了解更多
                        </Link>
                    ),
                },
                // {
                //   label: "链上哈希",
                //   value: productionInfo.productionTransId && (
                //     <Link
                //       style={{}}
                //       to={`certificate/${productionInfo.productionTransId}`}
                //     >
                //       <Ellipsis
                //         content={productionInfo.productionTransId}
                //       ></Ellipsis>
                //     </Link>
                //   ),
                // },
            ],
        });

        const logisticsInfo = traceCodeDetail?.logistics || {};
        const logisticsDetailItems = [
            {
                label: "物流企业",
                value: logisticsInfo.loEnterprises,
            },
            {
                label: "物流单号",
                value: logisticsInfo.loNumber,
            },
            {
                label: "装货地点",
                value: logisticsInfo.loadingLocation,
            },
            {
                label: "运输方式",
                value:
                    logisticsInfo.transportationType &&
                    // @ts-ignore
                    TRANSPORTATION_TYPE_CONSTANTS
                        .TRANSPORTATION_TYPE_MAP_BY_VALUE[
                        logisticsInfo.transportationType
                    ].name,
            },
            {
                label: "链上哈希",
                value: logisticsInfo.logisticsTransId && (
                    <Link
                        style={{}}
                        to={`certificate/${logisticsInfo.logisticsTransId}`}
                    >
                        <Ellipsis
                            content={logisticsInfo.logisticsTransId}
                        ></Ellipsis>
                    </Link>
                ),
            },
            {
                label: "卸货地点",
                value: logisticsInfo.unloadingLocation,
            },
            {
                // 卸货的链上哈希
                label: "链上哈希",
                value: logisticsInfo.unLoadLogisticsTransId && (
                    <Link
                        style={{}}
                        to={`certificate/${logisticsInfo.unLoadLogisticsTransId}`}
                    >
                        <Ellipsis
                            content={logisticsInfo.unLoadLogisticsTransId}
                        ></Ellipsis>
                    </Link>
                ),
            },
        ];

        return (
            <>
                <TraceBlock
                    title="产品信息"
                    config={[
                        {
                            items: productDetailItems,
                        },
                    ]}
                    // custom={<div><Button>按钮</Button></div> }
                ></TraceBlock>
                {/* <TraceBlock
                  title="原料溯源"
                  config={materialConfig}
              ></TraceBlock> */}
                {Object.keys(plantInfo).length !== 0 ? (
                    <TraceBlock
                        title="种植溯源"
                        config={[
                            {
                                items: plantDetailItems,
                            },
                        ]}
                    ></TraceBlock>
                ) : (
                    <></>
                )}

                <TraceBlock
                    title="生产溯源"
                    config={processConfig}
                    // custom={<div><Button>按钮</Button></div> }
                ></TraceBlock>
                {/* <TraceBlock
        title="物流溯源"
        config={[
          {
            items: logisticsDetailItems,
          },
        ]}
      ></TraceBlock> */}
            </>
        );
    };
    // 产品简介
    const renderProductTabInfo = () => {
        if (traceCodeProductDetailRequest.loading) {
            return (
                <div
                    style={{
                        paddingTop: "60px",
                        textAlign: "center",
                        color: "var(--adm-color-weak)",
                    }}
                >
                    <div style={{ fontSize: 24, marginBottom: 24 }}>
                        <DotLoading />
                    </div>
                    正在加载数据
                </div>
            );
        }
        const productBrief = traceCodeProductDetail?.productBrief || {};
        const productBriefItems = [
            {
                value: productBrief.productIntro && (
                    <div className={styles.introText} style={{ width: "100%" }}>
                        {productBrief.productIntro}
                    </div>
                ),
            },
            {
                value: productBrief.productVideo && (
                    <div
                        style={{
                            width: "100%",
                        }}
                    >
                        <PreviewVideo
                            preview={false}
                            src={video}
                        ></PreviewVideo>
                    </div>
                ),
            },
        ];
        if (productBrief.productImg && promotionPicData) {
            promotionPicData.forEach((img: string) => {
                productBriefItems.push({
                    value: (
                        <div
                            style={{
                                width: "100%",
                            }}
                        >
                            <PreviewImages
                                preview={false}
                                images={[img]}
                            ></PreviewImages>
                        </div>
                    ),
                });
            });
        }
        const productBriefConfig = [
            {
                items: productBriefItems,
            },
        ];

        const placeBrief = traceCodeProductDetail?.placeBrief || {};
        const placeBriefItems = [
            {
                value: placeBrief.placeIntro && (
                    <>
                        <div
                            className={styles.introText}
                            style={{ width: "100%" }}
                        >
                            {placeBrief.placeIntro}
                        </div>
                    </>
                ),
            },
            {
                value: placeBrief.placeVideo && (
                    <div
                        style={{
                            width: "100%",
                        }}
                    >
                        <PreviewVideo
                            preview={false}
                            src={pbVideo}
                        ></PreviewVideo>
                    </div>
                ),
            },
        ];
        if (pbImg) {
            pbImg.forEach((img: string) => {
                placeBriefItems.push({
                    value: (
                        <div
                            style={{
                                width: "100%",
                            }}
                        >
                            <PreviewImages
                                preview={false}
                                images={[img]}
                            ></PreviewImages>
                        </div>
                    ),
                });
            });
        }
        const placeBriefConfig = [
            {
                items: placeBriefItems,
            },
        ];

        const placeBasic = traceCodeProductDetail?.placeBasic || {};
        const placeBasicItems = [
            {
                label: "产地名称",
                value: placeBasic.placeName,
            },
            {
                label: "产地地址",
                value: placeBasic.placeAddress,
            },
            {
                label: "产地位置图",
                value: placeBasic.placeAptitude && pbApt && (
                    <PreviewImages images={[pbApt]}></PreviewImages>
                ),
            },
            {
                custom: placeBasic.placeTransId && (
                    <Link
                        className="tolink"
                        to={`certificate/${placeBasic.placeTransId}`}
                    >
                        <CheckCircleFill className="tolinkText" />{" "}
                        &nbsp;中移链溯源认证通过，点击了解更多
                    </Link>
                ),
            },
            // {
            //   label: "链上哈希",
            //   value: placeBasic.placeTransId && (
            //     <Link
            //       style={{}}
            //       to={`certificate/${placeBasic.placeTransId}`}
            //     >
            //       <Ellipsis content={placeBasic.placeTransId}></Ellipsis>
            //     </Link>
            //   ),
            // },
        ];
        const placeBasicConfig = [
            {
                items: placeBasicItems,
            },
        ];

        if (
            isTraceBlockEmpty(productBriefConfig) &&
            isTraceBlockEmpty(placeBriefConfig) &&
            isTraceBlockEmpty(placeBasicConfig)
        ) {
            return (
                <ErrorBlock
                    image={<div></div>}
                    title="暂无数据"
                    description=""
                />
            );
        }

        return (
            <>
                <TraceBlock
                    title="产品说明"
                    config={productBriefConfig}
                ></TraceBlock>
                <TraceBlock
                    title="产地说明"
                    config={placeBriefConfig}
                ></TraceBlock>
                <TraceBlock
                    title="产地详情"
                    config={placeBasicConfig}
                ></TraceBlock>
            </>
        );
    };
    // 质检信息
    const renderInspectionTabInfo = () => {
        if (traceCodeInspectionDetailRequest.loading) {
            return (
                <div
                    style={{
                        paddingTop: "60px",
                        textAlign: "center",
                        color: "var(--adm-color-weak)",
                    }}
                >
                    <div style={{ fontSize: 24, marginBottom: 24 }}>
                        <DotLoading />
                    </div>
                    正在加载数据
                </div>
            );
        }

        const inspectionConfig = traceCodeInspectionDetail.map(
            (item: any, index: number) => {
                return {
                    title: "质检详情",
                    items: [
                        {
                            label: "质检内容",
                            value: item.inspectionContent,
                        },
                        {
                            label: "质检结果",
                            value: item.inspectionResults && (
                                <div>
                                    {item.inspectionResults === "1" && "合格"}
                                    {item.inspectionResults === "2" && "不合格"}
                                </div>
                            ),
                        },
                        {
                            label: "质检报告",
                            value: item.inspectionReport &&
                                inspectionReportDec && (
                                    <PreviewImages
                                        images={[inspectionReportDec[index]]}
                                    ></PreviewImages>
                                ),
                        },
                        {
                            label: "质检机构",
                            value: item.inspectionInstitution,
                        },
                        {
                            custom: item.inspectionTransId && (
                                <Link
                                    className="tolink"
                                    to={`certificate/${item.inspectionTransId}`}
                                >
                                    <CheckCircleFill className="tolinkText" />{" "}
                                    &nbsp;中移链溯源认证通过，点击了解更多
                                </Link>
                            ),
                        },
                        // {
                        //     label: "链上哈希",
                        //     value: item.inspectionTransId && (
                        //         <Link
                        //             style={{}}
                        //             to={`certificate/${item.inspectionTransId}`}
                        //         >
                        //             <Ellipsis
                        //                 content={item.inspectionTransId}
                        //             ></Ellipsis>
                        //         </Link>
                        //     ),
                        // },
                    ],
                };
            },
        );

        if (isTraceBlockEmpty(inspectionConfig)) {
            return (
                <ErrorBlock
                    image={<div></div>}
                    title="暂无数据"
                    description=""
                />
            );
        }

        return (
            <>
                <TraceBlock
                    title="质检信息"
                    config={inspectionConfig}
                ></TraceBlock>
            </>
        );
    };
    // 生产商
    const renderOrgTabInfo = () => {
        if (traceCodeOrgDetailRequest.loading) {
            return (
                <div
                    style={{
                        paddingTop: "60px",
                        textAlign: "center",
                        color: "var(--adm-color-weak)",
                    }}
                >
                    <div style={{ fontSize: 24, marginBottom: 24 }}>
                        <DotLoading />
                    </div>
                    正在加载数据
                </div>
            );
        }

        const orgBrief = traceCodeOrgDetail.orgBrief || {};
        const orgBriefItems = [
            {
                value: orgBrief.introduce && (
                    <div className={styles.introText} style={{ width: "100%" }}>
                        {orgBrief.introduce}
                    </div>
                ),
            },
            {
                value: orgBrief.video && orgBriefVideo && (
                    <div
                        style={{
                            width: "100%",
                        }}
                    >
                        <PreviewVideo
                            preview={false}
                            src={orgBriefVideo}
                        ></PreviewVideo>
                    </div>
                ),
            },
        ];
        if (orgBrief.picture && orgBriefImg) {
            orgBriefImg.forEach((img: string) => {
                orgBriefItems.push({
                    value: (
                        <div
                            style={{
                                width: "100%",
                            }}
                        >
                            <PreviewImages
                                preview={false}
                                images={[img]}
                            ></PreviewImages>
                        </div>
                    ),
                });
            });
        }
        const orgBriefConfig = [
            {
                items: orgBriefItems,
            },
        ];

        const orgBasic = traceCodeOrgDetail?.orgBasic || {};
        const orgBasicItems = [
            {
                label: "企业名称",
                value: orgBasic.companyName,
            },
            {
                label: "企业地址",
                value: orgBasic.address,
            },
            {
                label: "统一社会信用代码",
                value: orgBasic.creditCode,
            },
            {
                label: "企业资质",
                value: orgBasic.qualification && orgBasicQualification && (
                    <PreviewImages
                        images={[orgBasicQualification]}
                    ></PreviewImages>
                ),
            },
            {
                custom: orgBasic.orgTransId && (
                    <Link
                        className="tolink"
                        to={`certificate/${orgBasic.orgTransId}`}
                    >
                        <CheckCircleFill className="tolinkText" />{" "}
                        &nbsp;中移链溯源认证通过，点击了解更多
                    </Link>
                ),
            },
            // {
            //   label: "链上哈希",
            //   value: orgBasic.orgTransId && (
            //     <Link style={{}} to={`certificate/${orgBasic.orgTransId}`}>
            //       <Ellipsis content={orgBasic.orgTransId}></Ellipsis>
            //     </Link>
            //   ),
            // },
        ];
        const orgBasicConfig = [
            {
                items: orgBasicItems,
            },
        ];

        if (
            isTraceBlockEmpty(orgBriefConfig) &&
            isTraceBlockEmpty(orgBasicConfig)
        ) {
            return (
                <ErrorBlock
                    image={<div></div>}
                    title="暂无数据"
                    description=""
                />
            );
        }
        return (
            <>
                <TraceBlock title="生产商" config={orgBriefConfig}></TraceBlock>
                <TraceBlock
                    title="企业基础信息"
                    config={orgBasicConfig}
                ></TraceBlock>
            </>
        );
    };
    const back = () => {
        setShowHome(true);
    };
    // 关闭视频
    const closeVideo = () => {
        if (VideoRef.current) {
            // 1. 暂停音频
            VideoRef.current.stopVideo();
            // 2. 获取视频进度
            let progress = VideoRef.current.getProgress();
            console.log(VideoRef.current.getProgress());
            // 3. 隐藏视频
            setShowVideo(false);
            // 4. 打开音频
            // setAudio(true);
            // // 5. 播放音频
            // if (AudioRef.current) {
            //     // 6. 设置进度
            //     AudioRef.current.currentTime = progress;
            //     // 7. 播放音频
            //     AudioRef.current.play();
            // }
        }
    };
    // 关闭音频
    const closeAudio = () => {
        setAudio(false);
        AudioRef.current.pause();
    };
    return (
        <div>
            <div
                className={[
                    classNames({
                        none: showHome,
                        block: !showHome,
                        imgbox: true,
                    }),
                    styles.block,
                ].join(" ")}
                style={{ height: "100vh" }}
            >
                {/* <h1 onClick={back}>首页</h1> */}
                <div>
                    <Image src={homeImg} fit="fill" className={styles.homeBg} />
                    <div className={styles.verticalTextContainer}>
                        <div className={styles.verticalText}>
                            {traceCodeDetail.openScreenTitle}
                        </div>
                    </div>
                    {/* <div className={styles.title}>
                        {traceCodeDetail.openScreenTitle}
                    </div> */}
                </div>
                <div className="trace_btn" onClick={back}>
                    <Button color="primary" fill="none">
                        点击溯源
                    </Button>
                </div>
                <div className={styles.text}>
                    <div className={styles.homeLogo}></div>
                    <div>
                        Copyright 2024 中移动信息技术有限公司 . All rights
                        reserved.
                    </div>
                </div>
            </div>
            <div
                className={classNames({
                    _global_page: true,
                    block: showHome,
                    none: !showHome,
                })}
            >
                {/* <div>
                  <PreviewVideo
                      className={styles.my_video_wrap}
                      // preview={showVideo}
                      preview={false}
                      src={videoSrc}
                  ></PreviewVideo>
                  <audio controls>
                      <source src={videoSrc} type="audio/mpeg" />
                  </audio>
                  <button onClick={closeVideo}>关闭视频</button>
              </div> */}

                {videoSrc && (
                    <>
                        <div
                            className={
                                classNames({
                                    none: !showVideo,
                                }) +
                                " " +
                                styles.video_wrap
                            }
                            style={{ width: "100%" }}
                        >
                            <span
                                className={classNames({
                                    close: true,
                                    none: false,
                                })}
                                onClick={closeVideo}
                            >
                                X
                            </span>
                            <PreviewVideo
                                style={{ height: "300px" }} // 固定高度为300px
                                ref={VideoRef}
                                preview={false}
                                src={videoSrc}
                            ></PreviewVideo>
                        </div>
                        {/* // 音频 */}
                        {/* <div
                          className={classNames({
                              none: !showAudio,
                          })}
                          style={{
                              position: "fixed",
                              top: "65%",
                              right: "0%",
                              zIndex: 10,
                          }}
                      >
                          <span
                              style={{
                                  position: "absolute",
                                  right: "7px",
                                  top: "-28px",
                                  background: "#f1f3f4",
                                  padding: "5px",
                                  borderRadius: "13px",
                                  color: "#000",
                                  cursor: "pointer",
                              }}
                              onClick={closeAudio}
                          >
                              关闭
                          </span>
                          <audio controls ref={AudioRef}>
                              <source src={videoSrc} type="audio/mpeg" />
                          </audio>
                      </div> */}
                    </>
                )}
                <div className="_global_pageScrollContent">
                    <div className={styles.cTrace}>
                        <NavBar color="black" backArrow={false}>
                            中移链溯源信息
                        </NavBar>

                        <div className={styles.traceFunction}>
                            {/* <div className={styles.traceFunction__info}>
                              <img
                                  className={styles.traceFunction__icon}
                                  src={iconWarning}
                              />
                              <span>
                                  {traceCodeDetail.searchCount === 1
                                      ? "正品认证 品质之选"
                                      : "多次验证 谨防假冒"}
                              </span>
                              <span>正品认证 品质之选</span>
                          </div> */}
                        </div>

                        <div className={styles.main}>
                            {traceCodeDetail.maxQuantity == 0 ? (
                                <div className={styles.traceFunction__suss}>
                                    <div
                                        className={styles.traceFunction__img}
                                    ></div>
                                    {/* <img src={var(--sess-bg-img)} alt="" /> */}
                                    <div>溯源验证通过</div>
                                </div>
                            ) : traceCodeDetail.maxQuantity >=
                              traceCodeDetail.searchCount ? (
                                <div className={styles.traceFunction__suss}>
                                    <div
                                        className={styles.traceFunction__img}
                                    ></div>
                                    {/* <img src={suss} alt="" /> */}
                                    <div>溯源验证通过</div>
                                </div>
                            ) : (
                                <div className={styles.traceFunction__info}>
                                    <div
                                        className={
                                            styles.traceFunction__info_img
                                        }
                                    ></div>
                                    {/* <img src={Info} alt="" /> */}
                                    <div>
                                        <div>风险提醒</div>
                                        <div
                                            className={
                                                styles.traceFunction__info_text
                                            }
                                        >
                                            该码已被多次查验，建议您核实产品来源谨防假冒
                                        </div>
                                    </div>
                                </div>
                            )}

                            <div className={styles.traceSearchInfo}>
                                <div className={styles.traceSearchInfo__item}>
                                    {/* <div
                                        className={
                                            styles.traceSearchInfo__label
                                        }
                                    >
                                        溯源码
                                    </div> */}
                                    <div
                                        className={
                                            styles.traceSearchInfo__value
                                        }
                                    >
                                        溯源码 {traceCodeDetail.code || "-"}
                                    </div>
                                </div>
                                {/* <div className={styles.traceSearchInfo__item}>
                              <div className={styles.traceSearchInfo__label}>
                                  首次查询
                              </div>
                              <div className={styles.traceSearchInfo__value}>
                                  {" "}
                                  {traceCodeDetail.firstTime
                                      ? dayjs(
                                            traceCodeDetail.firstTime,
                                        ).format("YYYY.MM.DD HH:mm:ss")
                                      : "-"}
                              </div>
                          </div>
                          <div className={styles.traceSearchInfo__item}>
                              <div className={styles.traceSearchInfo__label}>
                                  扫码次数
                              </div>
                              <div className={styles.traceSearchInfo__value}>
                                  {traceCodeDetail.searchCount !==
                                  undefined ? (
                                      <Link
                                          to={`scan-log/${urlState.traceCodeId}`}
                                          className={
                                              styles.traceSearchInfo__link
                                          }
                                      >
                                          {traceCodeDetail.searchCount}次
                                          <RightOutline></RightOutline>
                                      </Link>
                                  ) : (
                                      "-"
                                  )}
                              </div>
                          </div> */}
                            </div>

                            <Tabs
                                className={styles.traceTabs}
                                activeKey={traceTabsActiveKey}
                                onChange={(activeKey: string) => {
                                    const traceTabsKey =
                                        activeKey as TraceTabsKey;
                                    setTraceTabsActiveKey(traceTabsKey);
                                }}
                            >
                                <Tabs.Tab
                                    title="溯源信息"
                                    key={TraceTabsKey.trace}
                                >
                                    {renderTraceTabInfo()}
                                </Tabs.Tab>
                                <Tabs.Tab
                                    title="产品简介"
                                    key={TraceTabsKey.product}
                                >
                                    {renderProductTabInfo()}
                                </Tabs.Tab>
                                <Tabs.Tab
                                    title="质检信息"
                                    key={TraceTabsKey.inspection}
                                >
                                    {renderInspectionTabInfo()}
                                </Tabs.Tab>
                                <Tabs.Tab
                                    title="生产商"
                                    key={TraceTabsKey.enterprise}
                                >
                                    {renderOrgTabInfo()}
                                </Tabs.Tab>
                            </Tabs>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Trace;
