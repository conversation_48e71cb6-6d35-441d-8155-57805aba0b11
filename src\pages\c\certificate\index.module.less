.cCertificate {
    :global {
        .main {
            flex: 1;
            padding: 20px;
            background: var(--page-bg-color);

            .certificate {
                position: relative;
                width: 100%;
                height: 100%;
                background:
                    url("@/assets/imgs/c/certificate/header.png") no-repeat
                        top/100%,
                    // url("@/assets/imgs/c/certificate/footer.png") no-repeat bottom/100% ,
                    #fff;
                display: flex;
                flex-direction: column;
                align-items: center;

                &__badge {
                    // margin-top: 29px;
                    margin-top: 5vh;

                    img {
                        width: 80px;
                        height: 77px;
                    }
                }

                &__title {
                    // margin-top: 7px;
                    margin-top: 1vh;
                    color: #333333;
                    font-weight: 500;
                    font-size: 22px;
                }

                &__content {
                    flex: 1;
                    margin-top: 4vh;
                    box-sizing: border-box;
                    width: 100%;
                    padding: 0 min(12.6vw, 61.2px);
                    padding-bottom: 17vh;
                    display: flex;
                    flex-direction: column;
                    font-family: monospace; // 等宽字体
                    justify-content: flex-start;

                    &-item {
                        width: 100%;
                        display: flex;
                        flex-direction: row;
                        align-items: baseline;
                        margin-bottom: 7px;

                        &-label {
                            text-align: right;
                            width: 500px;
                            // color: #909090;
                            color: black;
                            font-size: 14px;
                        }

                        &-value {
                            // width: 100%;
                            width: 640px;
                            margin-top: 2px;
                            padding: 6px 0 6px 0;
                            word-break: break-all;
                            // text-align: center;
                            color: black;
                            font-size: 14px;
                            line-height: 16px;
                            // background: #ebf2ff;
                            border-radius: 4px;

                            &--main {
                                font-size: 18px;
                            }
                        }
                    }

                    .text {
                        font-size: 13px;
                        margin-top: 60px;
                    }

                    .chapter_Icon {
                        width: 120px;
                        height: 100px;
                        position: relative;
                        /* top: 10%; */
                        left: 62%;
                        bottom: 24%;

                        img {
                            width: 100%;
                            opacity: 0.7;
                        }
                    }
                }

                &__footer {
                    position: absolute;
                    bottom: 0;
                    width: 100%;

                    img {
                        width: 100%;
                    }
                }
            }
        }
    }
}
